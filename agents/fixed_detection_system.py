#!/usr/bin/env python3
"""
Fixed Detection System - Hybrid Approach
Bypasses CrewAI tool execution issues by using direct API calls with LLM analysis
"""

import os
import json
import time
import requests
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
from dataclasses import dataclass
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TokenDetectionResult:
    """Structured token detection result"""
    address: str
    chain: str
    symbol: Optional[str]
    name: Optional[str]
    created_at: datetime
    liquidity_usd: float
    holder_count: int
    confidence_score: float
    analysis: str
    recommendation: str

class DirectAPIDetectionAgent:
    """
    Direct API-based detection agent that bypasses CrewAI tool execution issues
    Uses LLM for analysis but handles data fetching directly
    """
    
    def __init__(self):
        self.groq_api_key = os.getenv("GROQ_API_KEY")
        self.openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
        
    def get_new_pairs_dexscreener(self, chain: str = "solana") -> Dict[str, Any]:
        """Direct API call to DEXScreener - bypasses CrewAI tool system"""
        try:
            current_time = time.time()
            search_queries = ["meme", "sol", "token", "new"]
            all_pairs = []
            
            for query in search_queries:
                try:
                    url = f"https://api.dexscreener.com/latest/dex/search?q={query}"
                    response = requests.get(url, timeout=15)
                    
                    if response.status_code == 200:
                        data = response.json()
                        pairs = data.get('pairs', [])
                        
                        # Filter for specified chain and recent pairs
                        for pair in pairs:
                            if pair.get('chainId') == chain:
                                created_at = pair.get('pairCreatedAt')
                                if created_at:
                                    try:
                                        created_timestamp = int(created_at) / 1000
                                        age_hours = (current_time - created_timestamp) / 3600
                                        
                                        # Only include pairs created in last 24 hours
                                        if age_hours <= 24:
                                            pair_data = {
                                                'symbol': pair.get('baseToken', {}).get('symbol', 'Unknown'),
                                                'address': pair.get('baseToken', {}).get('address', ''),
                                                'chainId': pair.get('chainId'),
                                                'age_hours': round(age_hours, 2),
                                                'priceUsd': pair.get('priceUsd', '0'),
                                                'volume24h': pair.get('volume', {}).get('h24', 0),
                                                'liquidity': pair.get('liquidity', {}).get('usd', 0),
                                                'pairAddress': pair.get('pairAddress', ''),
                                                'dexId': pair.get('dexId', ''),
                                                'created_at': created_at
                                            }
                                            all_pairs.append(pair_data)
                                    except (ValueError, TypeError):
                                        continue
                                        
                except requests.RequestException as e:
                    logger.warning(f"DEXScreener query failed for {query}: {str(e)}")
                    continue
                    
            # Remove duplicates by address
            seen_addresses = set()
            unique_pairs = []
            for pair in all_pairs:
                address = pair.get('address', '')
                if address and address not in seen_addresses:
                    seen_addresses.add(address)
                    unique_pairs.append(pair)
                    
            # Sort by creation time (newest first)
            unique_pairs.sort(key=lambda x: int(x.get('created_at', 0)), reverse=True)
            
            return {
                'status': 'success',
                'pairs_found': len(unique_pairs),
                'chain': chain,
                'pairs': unique_pairs[:10]  # Top 10 newest
            }
            
        except Exception as e:
            logger.error(f"DEXScreener API error: {str(e)}")
            return {
                'status': 'error',
                'message': str(e),
                'pairs': []
            }
    
    def analyze_with_llm(self, token_data: Dict[str, Any]) -> Dict[str, Any]:
        """Use LLM for analysis without CrewAI tool system"""
        try:
            # Prepare analysis prompt
            prompt = f"""
            Analyze this token data and provide a structured assessment:
            
            Token Data:
            - Symbol: {token_data.get('symbol', 'Unknown')}
            - Address: {token_data.get('address', 'Unknown')}
            - Chain: {token_data.get('chainId', 'Unknown')}
            - Age: {token_data.get('age_hours', 0)} hours
            - Liquidity: ${token_data.get('liquidity', 0):,.2f}
            - Volume 24h: ${token_data.get('volume24h', 0):,.2f}
            - Price: ${token_data.get('priceUsd', '0')}
            
            Provide analysis in this exact JSON format:
            {{
                "confidence_score": 0.85,
                "analysis": "Brief analysis of the token's viability",
                "recommendation": "PROCEED_TO_ANALYSIS or SKIP_LOW_CONFIDENCE",
                "risk_factors": ["factor1", "factor2"],
                "opportunity_score": 0.75
            }}
            
            Consider:
            - Liquidity adequacy (minimum $5,000 for Solana)
            - Age appropriateness (very new tokens are higher risk)
            - Volume/liquidity ratio
            - Price stability indicators
            
            Respond with ONLY the JSON, no other text.
            """
            
            # Use Groq API directly
            headers = {
                "Authorization": f"Bearer {self.groq_api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": "llama3-70b-8192",
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.1,
                "max_tokens": 500
            }
            
            response = requests.post(
                "https://api.groq.com/openai/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                
                # Parse JSON response
                try:
                    analysis = json.loads(content)
                    return analysis
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse LLM JSON response: {content}")
                    return {
                        "confidence_score": 0.5,
                        "analysis": "Analysis parsing failed",
                        "recommendation": "SKIP_LOW_CONFIDENCE",
                        "risk_factors": ["parsing_error"],
                        "opportunity_score": 0.3
                    }
            else:
                logger.error(f"Groq API error: {response.status_code} - {response.text}")
                return {
                    "confidence_score": 0.0,
                    "analysis": "LLM analysis failed",
                    "recommendation": "SKIP_LOW_CONFIDENCE",
                    "risk_factors": ["llm_error"],
                    "opportunity_score": 0.0
                }
                
        except Exception as e:
            logger.error(f"LLM analysis error: {str(e)}")
            return {
                "confidence_score": 0.0,
                "analysis": f"Analysis error: {str(e)}",
                "recommendation": "SKIP_LOW_CONFIDENCE",
                "risk_factors": ["system_error"],
                "opportunity_score": 0.0
            }
    
    def detect_and_analyze_tokens(self, chain: str = "solana") -> List[TokenDetectionResult]:
        """Main detection and analysis pipeline"""
        logger.info(f"Starting token detection for {chain}")
        
        # Step 1: Get raw token data
        raw_data = self.get_new_pairs_dexscreener(chain)
        
        if raw_data['status'] != 'success':
            logger.error(f"Token detection failed: {raw_data.get('message', 'Unknown error')}")
            return []
        
        pairs = raw_data.get('pairs', [])
        logger.info(f"Found {len(pairs)} potential tokens")
        
        # Step 2: Analyze each token with LLM
        results = []
        for pair in pairs:
            try:
                analysis = self.analyze_with_llm(pair)
                
                # Create structured result
                result = TokenDetectionResult(
                    address=pair.get('address', ''),
                    chain=pair.get('chainId', chain),
                    symbol=pair.get('symbol'),
                    name=pair.get('symbol'),  # Use symbol as name for now
                    created_at=datetime.now(timezone.utc),
                    liquidity_usd=float(pair.get('liquidity', 0)),
                    holder_count=0,  # Not available from DEXScreener
                    confidence_score=analysis.get('confidence_score', 0.0),
                    analysis=analysis.get('analysis', ''),
                    recommendation=analysis.get('recommendation', 'SKIP_LOW_CONFIDENCE')
                )
                
                # Include all results for testing (lower threshold)
                if result.confidence_score >= 0.5:  # Lowered for testing
                    results.append(result)
                    logger.info(f"✅ Token detected: {result.symbol} (confidence: {result.confidence_score})")
                else:
                    logger.info(f"⚠️  Low-confidence token: {pair.get('symbol')} (confidence: {analysis.get('confidence_score', 0.0)})")
                    
            except Exception as e:
                logger.error(f"Token analysis failed for {pair.get('symbol', 'Unknown')}: {str(e)}")
                continue
        
        logger.info(f"Detection complete: {len(results)} high-confidence tokens identified")
        return results

# Test function
def test_fixed_detection_system():
    """Test the fixed detection system"""
    print("=" * 60)
    print("TESTING FIXED DETECTION SYSTEM")
    print("=" * 60)
    
    try:
        # Create detection agent
        agent = DirectAPIDetectionAgent()
        
        # Test detection
        print("🔍 Starting token detection...")
        results = agent.detect_and_analyze_tokens("solana")
        
        print(f"\n✅ Detection completed!")
        print(f"📊 Results: {len(results)} high-confidence tokens found")
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result.symbol} ({result.address[:8]}...)")
            print(f"   💰 Liquidity: ${result.liquidity_usd:,.2f}")
            print(f"   🎯 Confidence: {result.confidence_score:.2f}")
            print(f"   📝 Analysis: {result.analysis}")
            print(f"   ✅ Recommendation: {result.recommendation}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_fixed_detection_system()
