2025-07-19 13:15:17,209 - __main__ - INFO - 🚀 Starting Comprehensive System Test Suite
2025-07-19 13:15:17,209 - __main__ - INFO - Senior Engineering Level Validation
2025-07-19 13:15:17,209 - __main__ - INFO - ================================================================================
2025-07-19 13:15:17,209 - __main__ - INFO - TEST 1: ENVIRONMENT & CONFIGURATION
2025-07-19 13:15:17,209 - __main__ - INFO - ================================================================================
2025-07-19 13:15:17,534 - core.config - INFO - Configuration loaded successfully for environment: development
2025-07-19 13:15:17,534 - __main__ - INFO - ✅ PASS environment_configuration (0.325s) - All 5 required env vars present; Settings loaded: env=development; infura API key loaded; groq API key loaded; helius API key loaded
2025-07-19 13:15:17,534 - __main__ - INFO - 
TEST 2: REDIS CONNECTIVITY
2025-07-19 13:15:17,534 - __main__ - INFO - ----------------------------------------
2025-07-19 13:15:17,585 - __main__ - INFO - ✅ PASS redis_connectivity (0.051s) - Redis ping successful; Redis read/write operations successful; Redis performance good: 0.024s for 100 ops
2025-07-19 13:15:17,585 - __main__ - INFO - 
TEST 3: API ENDPOINTS COMPREHENSIVE
2025-07-19 13:15:17,585 - __main__ - INFO - ----------------------------------------
2025-07-19 13:15:17,695 - __main__ - INFO - ✅ PASS api_DEXScreener_Search (0.109s) - Status 200 OK; Response time: 0.109s; JSON parsed successfully; Key 'pairs' present; Found 30 pairs
2025-07-19 13:15:17,747 - __main__ - INFO - ✅ PASS api_DEXScreener_Token_Lookup (0.052s) - Status 200 OK; Response time: 0.050s; JSON parsed successfully; Key 'pairs' present; Found 30 pairs
2025-07-19 13:15:18,127 - __main__ - INFO - ⚠️ WARN api_Birdeye_Health_Check (0.379s) - Status 200 OK; Response time: 0.374s; Request failed: 200, message='Attempt to decode JSON with unexpected mimetype: text/html; charset=utf-8', url='https://docs.birdeye.so'
2025-07-19 13:15:18,128 - __main__ - INFO - ✅ PASS api_endpoints_comprehensive (0.542s) - Tested 3 endpoints
2025-07-19 13:15:18,128 - __main__ - INFO - 
TEST 4: DETECTION FUNCTIONS
2025-07-19 13:15:18,128 - __main__ - INFO - ----------------------------------------
2025-07-19 13:15:19,266 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-07-19 13:15:21,488 - agents.detection_agents - ERROR - DEXScreener detection failed: 'Tool' object is not callable
2025-07-19 13:15:21,488 - agents.detection_agents - ERROR - Birdeye detection failed: 'Tool' object is not callable
2025-07-19 13:15:21,488 - agents.detection_agents - ERROR - Direct Solana monitoring failed: 'Tool' object is not callable
2025-07-19 13:15:21,488 - agents.detection_agents - INFO - Total unique tokens found: 0 (from 0 raw results)
2025-07-19 13:15:21,488 - __main__ - INFO - ❌ FAIL detection_functions (3.360s) - DEXScreener function failed: asyncio.run() cannot be called from a running event loop; Comprehensive: 0 results in 0.000s
2025-07-19 13:15:21,488 - __main__ - INFO - 
TEST 5: AGENT INITIALIZATION
2025-07-19 13:15:21,488 - __main__ - INFO - ----------------------------------------
2025-07-19 13:15:21,488 - agents.agent_coordinator - INFO - Initializing MemeGuard Pro Agent System...
2025-07-19 13:15:21,490 - agents.agent_coordinator - INFO - Redis connection established
2025-07-19 13:15:21,491 - agents.agent_coordinator - INFO - API Manager initialized successfully
2025-07-19 13:15:21,491 - agents.agent_coordinator - INFO - Initializing detection coordinator...
2025-07-19 13:15:21,610 - agents.agent_coordinator - INFO - Detection coordinator initialized successfully. Available coordinators: ['detection']
2025-07-19 13:15:21,610 - agents.agent_coordinator - INFO - All agent coordinators initialized successfully
2025-07-19 13:15:21,611 - agents.agent_coordinator - INFO - Clearing 3 stale items from system_events
2025-07-19 13:15:21,612 - agents.agent_coordinator - INFO - System monitoring initialized
2025-07-19 13:15:21,612 - agents.agent_coordinator - INFO - MemeGuard Pro Agent System initialized successfully
2025-07-19 13:15:21,613 - agents.agent_coordinator - INFO - Shutting down MemeGuard Pro Agent System...
2025-07-19 13:15:21,613 - agents.agent_coordinator - INFO - Redis connection closed
2025-07-19 13:15:21,613 - agents.agent_coordinator - INFO - API Manager shutdown complete
2025-07-19 13:15:21,613 - agents.agent_coordinator - INFO - API manager shutdown complete
2025-07-19 13:15:21,613 - agents.agent_coordinator - INFO - MemeGuard Pro Agent System shutdown complete
2025-07-19 13:15:21,613 - __main__ - INFO - ✅ PASS agent_initialization (0.125s) - AgentSystemManager created; Coordinator initialized in 0.124s; Created 1 coordinators: ['detection']; Detection coordinator available; Coordinator Redis connection OK; Coordinator shutdown successful
2025-07-19 13:15:21,613 - __main__ - INFO - 
TEST 6: SYSTEM RESOURCES
2025-07-19 13:15:21,613 - __main__ - INFO - ----------------------------------------
2025-07-19 13:15:22,667 - __main__ - INFO - ✅ PASS system_resources (1.053s) - CPU usage: 23.1%; Memory usage: 80.1%; Disk usage: 1.6%; Network connectivity OK; Related processes: Redis (PID: 7722)
2025-07-19 13:15:22,667 - __main__ - INFO - 
TEST 7: ERROR HANDLING
2025-07-19 13:15:22,667 - __main__ - INFO - ----------------------------------------
2025-07-19 13:15:22,673 - __main__ - INFO - ✅ PASS error_handling (0.006s) - Timeout handling works correctly; API key test error: Session is closed; Redis connection failure properly handled
2025-07-19 13:15:22,675 - __main__ - INFO - 📊 Test results saved to system_test_results.json
