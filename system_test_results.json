{"summary": {"total_tests": 10, "passed_tests": 8, "failed_tests": 2, "warnings": 1, "performance_issues": 0}, "results": {"environment_configuration": {"success": true, "details": "All 5 required env vars present; Settings loaded: env=development; infura API key loaded; groq API key loaded; helius API key loaded", "duration": 0.32456302642822266, "timestamp": "2025-07-19T13:15:17.534198", "warning": false}, "redis_connectivity": {"success": true, "details": "Redis ping successful; Redis read/write operations successful; Redis performance good: 0.024s for 100 ops", "duration": 0.050920963287353516, "timestamp": "2025-07-19T13:15:17.585242", "warning": false}, "api_DEXScreener_Search": {"success": true, "details": "Status 200 OK; Response time: 0.109s; JSON parsed successfully; Key 'pairs' present; Found 30 pairs", "duration": 0.1094827651977539, "timestamp": "2025-07-19T13:15:17.695828", "warning": false}, "api_DEXScreener_Token_Lookup": {"success": true, "details": "Status 200 OK; Response time: 0.050s; JSON parsed successfully; Key 'pairs' present; Found 30 pairs", "duration": 0.05161714553833008, "timestamp": "2025-07-19T13:15:17.747609", "warning": false}, "api_Birdeye_Health_Check": {"success": false, "details": "Status 200 OK; Response time: 0.374s; Request failed: 200, message='Attempt to decode JSON with unexpected mimetype: text/html; charset=utf-8', url='https://docs.birdeye.so'", "duration": 0.3792734146118164, "timestamp": "2025-07-19T13:15:18.127031", "warning": true}, "api_endpoints_comprehensive": {"success": true, "details": "Tested 3 endpoints", "duration": 0.5424928665161133, "timestamp": "2025-07-19T13:15:18.127990", "warning": false}, "detection_functions": {"success": false, "details": "DEXScreener function failed: asyncio.run() cannot be called from a running event loop; Comprehensive: 0 results in 0.000s", "duration": 3.360193967819214, "timestamp": "2025-07-19T13:15:21.488547", "warning": false}, "agent_initialization": {"success": true, "details": "AgentSystemManager created; Coordinator initialized in 0.124s; Created 1 coordinators: ['detection']; Detection coordinator available; Coordinator <PERSON><PERSON> connection OK; Coordinator shutdown successful", "duration": 0.12519598007202148, "timestamp": "2025-07-19T13:15:21.613849", "warning": false}, "system_resources": {"success": true, "details": "CPU usage: 23.1%; Memory usage: 80.1%; Disk usage: 1.6%; Network connectivity OK; Related processes: Redis (PID: 7722)", "duration": 1.0531678199768066, "timestamp": "2025-07-19T13:15:22.667137", "warning": false}, "error_handling": {"success": true, "details": "Timeout handling works correctly; API key test error: Session is closed; Redis connection failure properly handled", "duration": 0.006457090377807617, "timestamp": "2025-07-19T13:15:22.673817", "warning": false}}, "timestamp": "2025-07-19T13:15:22.675188", "duration": 5.465802907943726}