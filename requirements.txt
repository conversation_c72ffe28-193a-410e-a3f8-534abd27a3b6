# MemeGuard Pro v4.0 - Production Dependencies
# Complete dependency list for PhD-level AI agent system

# Core AI/ML Framework
crewai>=0.80.0
langchain>=0.3.0
langchain-openai>=0.2.0
openai>=1.50.0

# Data Processing & Analytics  
pandas>=2.2.0
numpy>=1.26.0
scipy>=1.12.0
scikit-learn>=1.4.0

# Async/Networking
aiohttp>=3.9.0
asyncio-mqtt>=0.16.0
websockets>=12.0

# Database & Caching
redis[hiredis]>=5.0.0
asyncpg>=0.29.0
psycopg2-binary>=2.9.0
sqlalchemy[asyncio]>=2.0.0
alembic>=1.13.0

# Vector Database
chromadb>=0.5.0
sentence-transformers>=2.3.0

# Blockchain & Crypto APIs
web3>=6.15.0
solana>=0.34.0
base58>=2.1.1
eth-account>=0.11.0

# HTTP Clients & API Tools
requests>=2.31.0
httpx>=0.27.0
urllib3>=2.2.0

# Configuration & Environment
python-dotenv>=1.0.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Logging & Monitoring  
structlog>=24.1.0
prometheus-client>=0.20.0
sentry-sdk>=1.40.0

# Time Series & Financial Data
yfinance>=0.2.28
ccxt>=4.2.0
ta>=0.10.2

# Notification Systems
discord.py>=2.3.0
twilio>=8.12.0

# System Monitoring
psutil>=5.9.0
GPUtil>=1.4.0

# Security & Encryption
cryptography>=42.0.0
bcrypt>=4.1.0
PyJWT>=2.8.0

# Testing & Development
pytest>=8.0.0
pytest-asyncio>=0.23.0
pytest-mock>=3.12.0
black>=24.0.0
isort>=5.13.0
flake8>=7.0.0

# Documentation
sphinx>=7.2.0
sphinx-rtd-theme>=2.0.0

# Jupyter/Research
jupyter>=1.0.0
ipykernel>=6.29.0
matplotlib>=3.8.0
seaborn>=0.13.0
plotly>=5.18.0

# FastAPI Web Framework
fastapi>=0.109.0
uvicorn[standard]>=0.27.0
python-multipart>=0.0.9

# Background Tasks
celery>=5.3.0
kombu>=5.3.0

# File Processing
openpyxl>=3.1.0
xlsxwriter>=3.2.0

# Advanced ML Models (Optional)
torch>=2.1.0
transformers>=4.36.0
datasets>=2.16.0

# Development Tools
pre-commit>=3.6.0
mypy>=1.8.0
coverage>=7.4.0

# Production Deployment
gunicorn>=21.2.0
docker>=7.0.0
kubernetes>=29.0.0

# Environment-specific
python-decouple>=3.8

# Additional Utilities
click>=8.1.0
tqdm>=4.66.0
colorama>=0.4.6
tabulate>=0.9.0

# Version constraints for stability
# Ensure compatibility with Python 3.11+
setuptools>=69.0.0
wheel>=0.42.0
pip>=24.0.0
