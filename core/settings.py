"""
MemeGuard Pro - Settings and Configuration Management
Centralized configuration with validation and environment support
"""

import os
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass, field
from dotenv import load_dotenv

from .exceptions import ConfigurationError

logger = logging.getLogger(__name__)


@dataclass
class Settings:
    """Application settings with validation and defaults"""
    
    # Environment
    environment: str = "development"
    debug: bool = False
    test_mode: bool = False
    
    # Logging
    log_level: str = "INFO"
    sentry_dsn: Optional[str] = None
    
    # API Keys
    openrouter_api_key: Optional[str] = None
    groq_api_key: Optional[str] = None
    helius_api_key: Optional[str] = None
    infura_api_key: Optional[str] = None
    moralis_api_key: Optional[str] = None
    birdeye_api_key: Optional[str] = None
    apify_api_key: Optional[str] = None
    etherscan_api_key: Optional[str] = None
    tokensniffer_api_key: Optional[str] = None
    
    # Database
    redis_url: str = "redis://localhost:6379"
    database_url: str = "postgresql://user:password@localhost:5432/memeguard"
    
    # System Configuration
    detection_interval_seconds: int = 30
    max_tokens_per_cycle: int = 50
    analysis_timeout_seconds: int = 300
    max_concurrent_analyses: int = 10
    consensus_timeout_seconds: int = 60
    min_consensus_votes: int = 3
    
    # Risk Management
    max_position_size: float = 0.05
    max_total_meme_allocation: float = 0.20
    default_stop_loss: float = -0.50
    
    # Rate Limiting
    api_rate_limit: int = 1000
    
    # Notification Settings
    discord_webhook_url: Optional[str] = None
    smtp_server: Optional[str] = None
    smtp_port: int = 587
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    alert_email_recipient: Optional[str] = None
    
    # Advanced Configuration
    chromadb_persist_directory: str = "./chroma_db"
    chromadb_collection_name: str = "token_analysis"
    llm_temperature: float = 0.1
    llm_max_tokens: int = 3000
    llm_timeout_seconds: int = 120
    
    # Alert Filtering
    min_confidence_threshold: float = 0.7
    alert_cooldown_minutes: int = 60
    
    def __post_init__(self):
        """Validate settings after initialization"""
        self._validate_settings()
    
    def _validate_settings(self):
        """Validate configuration values"""
        
        # Validate environment
        valid_environments = ["development", "staging", "production"]
        if self.environment not in valid_environments:
            raise ConfigurationError(
                f"Invalid environment: {self.environment}. Must be one of: {valid_environments}",
                config_key="environment",
                config_value=self.environment
            )
        
        # Validate log level
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level.upper() not in valid_log_levels:
            raise ConfigurationError(
                f"Invalid log level: {self.log_level}. Must be one of: {valid_log_levels}",
                config_key="log_level",
                config_value=self.log_level
            )
        
        # Validate numeric ranges
        if self.detection_interval_seconds < 1:
            raise ConfigurationError(
                "Detection interval must be at least 1 second",
                config_key="detection_interval_seconds",
                config_value=self.detection_interval_seconds
            )
        
        if self.max_position_size <= 0 or self.max_position_size > 1:
            raise ConfigurationError(
                "Max position size must be between 0 and 1",
                config_key="max_position_size",
                config_value=self.max_position_size
            )
        
        if self.llm_temperature < 0 or self.llm_temperature > 2:
            raise ConfigurationError(
                "LLM temperature must be between 0 and 2",
                config_key="llm_temperature",
                config_value=self.llm_temperature
            )
    
    def is_development(self) -> bool:
        """Check if running in development mode"""
        return self.environment == "development"
    
    def is_production(self) -> bool:
        """Check if running in production mode"""
        return self.environment == "production"
    
    def get_api_key(self, service: str) -> Optional[str]:
        """Get API key for a specific service"""
        key_mapping = {
            'openrouter': self.openrouter_api_key,
            'groq': self.groq_api_key,
            'helius': self.helius_api_key,
            'infura': self.infura_api_key,
            'moralis': self.moralis_api_key,
            'birdeye': self.birdeye_api_key,
            'apify': self.apify_api_key,
            'etherscan': self.etherscan_api_key,
            'tokensniffer': self.tokensniffer_api_key,
        }
        return key_mapping.get(service.lower())
    
    def get_database_url(self) -> str:
        """Get database URL"""
        return self.database_url
    
    def get_redis_url(self) -> str:
        """Get Redis URL"""
        return self.redis_url


def load_settings_from_env(env_file: Optional[str] = None) -> Settings:
    """Load settings from environment variables"""
    
    # Load .env file if it exists
    if env_file is None:
        env_file = os.getenv('ENV_FILE', '.env')
    
    if Path(env_file).exists():
        load_dotenv(env_file)
        logger.info(f"Loaded environment from {env_file}")
    
    return Settings(
        # Environment
        environment=os.getenv('ENVIRONMENT', 'development'),
        debug=os.getenv('DEBUG', 'false').lower() == 'true',
        test_mode=os.getenv('TEST_MODE', 'false').lower() == 'true',
        
        # Logging
        log_level=os.getenv('LOG_LEVEL', 'INFO'),
        sentry_dsn=os.getenv('SENTRY_DSN'),
        
        # API Keys
        openrouter_api_key=os.getenv('OPENROUTER_API_KEY'),
        groq_api_key=os.getenv('GROQ_API_KEY'),
        helius_api_key=os.getenv('API_HELIUS_API_KEY'),
        infura_api_key=os.getenv('API_INFURA_API_KEY'),
        moralis_api_key=os.getenv('API_MORALIS_API_KEY'),
        birdeye_api_key=os.getenv('API_BIRDEYE_API_KEY'),
        apify_api_key=os.getenv('API_APIFY_API_KEY'),
        etherscan_api_key=os.getenv('API_ETHERSCAN_API_KEY'),
        tokensniffer_api_key=os.getenv('API_TOKENSNIFFER_KEY'),
        
        # Database
        redis_url=os.getenv('REDIS_URL', 'redis://localhost:6379'),
        database_url=os.getenv('DATABASE_URL', 'postgresql://user:password@localhost:5432/memeguard'),
        
        # System Configuration
        detection_interval_seconds=int(os.getenv('DETECTION_INTERVAL_SECONDS', '30')),
        max_tokens_per_cycle=int(os.getenv('MAX_TOKENS_PER_CYCLE', '50')),
        analysis_timeout_seconds=int(os.getenv('ANALYSIS_TIMEOUT_SECONDS', '300')),
        max_concurrent_analyses=int(os.getenv('MAX_CONCURRENT_ANALYSES', '10')),
        consensus_timeout_seconds=int(os.getenv('CONSENSUS_TIMEOUT_SECONDS', '60')),
        min_consensus_votes=int(os.getenv('MIN_CONSENSUS_VOTES', '3')),
        
        # Risk Management
        max_position_size=float(os.getenv('MAX_POSITION_SIZE', '0.05')),
        max_total_meme_allocation=float(os.getenv('MAX_TOTAL_MEME_ALLOCATION', '0.20')),
        default_stop_loss=float(os.getenv('DEFAULT_STOP_LOSS', '-0.50')),
        
        # Rate Limiting
        api_rate_limit=int(os.getenv('API_RATE_LIMIT', '1000')),
        
        # Notification Settings
        discord_webhook_url=os.getenv('DISCORD_WEBHOOK_URL'),
        smtp_server=os.getenv('SMTP_SERVER'),
        smtp_port=int(os.getenv('SMTP_PORT', '587')),
        smtp_username=os.getenv('SMTP_USERNAME'),
        smtp_password=os.getenv('SMTP_PASSWORD'),
        alert_email_recipient=os.getenv('ALERT_EMAIL_RECIPIENT'),
        
        # Advanced Configuration
        chromadb_persist_directory=os.getenv('CHROMADB_PERSIST_DIRECTORY', './chroma_db'),
        chromadb_collection_name=os.getenv('CHROMADB_COLLECTION_NAME', 'token_analysis'),
        llm_temperature=float(os.getenv('LLM_TEMPERATURE', '0.1')),
        llm_max_tokens=int(os.getenv('LLM_MAX_TOKENS', '3000')),
        llm_timeout_seconds=int(os.getenv('LLM_TIMEOUT_SECONDS', '120')),
        
        # Alert Filtering
        min_confidence_threshold=float(os.getenv('MIN_CONFIDENCE_THRESHOLD', '0.7')),
        alert_cooldown_minutes=int(os.getenv('ALERT_COOLDOWN_MINUTES', '60')),
    )


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get global settings instance"""
    global _settings
    if _settings is None:
        _settings = load_settings_from_env()
    return _settings


def validate_required_settings() -> None:
    """Validate that all required settings are present"""
    settings = get_settings()
    
    # Required API keys for basic functionality
    required_keys = {
        'openrouter_api_key': 'OPENROUTER_API_KEY',
        'helius_api_key': 'API_HELIUS_API_KEY',
        'infura_api_key': 'API_INFURA_API_KEY',
    }
    
    missing_keys = []
    for attr_name, env_name in required_keys.items():
        if not getattr(settings, attr_name):
            missing_keys.append(env_name)
    
    if missing_keys:
        raise ConfigurationError(
            f"Missing required API keys: {', '.join(missing_keys)}. "
            f"Please check your .env file and ensure all required keys are set.",
            details={'missing_keys': missing_keys}
        )
    
    logger.info("All required settings validated successfully")


def reload_settings() -> Settings:
    """Reload settings from environment"""
    global _settings
    _settings = None
    return get_settings()