"""
Unit tests for LLM clients
"""

import pytest
import asyncio
import aiohttp
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from datetime import datetime

from core.llm.clients import OpenRouterClient, GroqClient
from core.llm.models import (
    LLMRequest, LLMResponse, ModelConfig, ModelProvider, ModelType,
    DEEPSEEK_R1_CONFIG, LLAMA_3_3_70B_CONFIG
)
from core.exceptions import APIError, RateLimitError

# Configure pytest-asyncio
pytestmark = pytest.mark.asyncio


class TestOpenRouterClient:
    """Test OpenRouter client implementation"""
    
    @pytest.fixture
    def client(self):
        """Create OpenRouter client for testing"""
        return OpenRouterClient(api_key="test_key")
    
    @pytest.fixture
    def sample_request(self):
        """Create sample LLM request"""
        return LLMRequest(
            prompt="Test prompt",
            model_config=DEEPSEEK_R1_CONFIG,
            system_prompt="Test system prompt",
            correlation_id="test-123",
            agent_id="test-agent"
        )
    
    @pytest.fixture
    def mock_response_data(self):
        """Mock OpenRouter API response"""
        return {
            "choices": [{
                "message": {"content": "Test response"},
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 20,
                "total_tokens": 30
            },
            "model": "deepseek/deepseek-r1-distill-llama-70b"
        }
    
    async def test_prepare_headers(self, client, sample_request):
        """Test header preparation"""
        headers = await client._prepare_headers(sample_request)
        
        assert headers["Authorization"] == "Bearer test_key"
        assert headers["Content-Type"] == "application/json"
        assert headers["HTTP-Referer"] == "https://memeguard.pro"
        assert headers["X-Title"] == "MemeGuard-Pro"
        assert headers["X-OpenRouter-Provider"] == "DeepSeek"
    
    async def test_prepare_payload(self, client, sample_request):
        """Test payload preparation"""
        payload = await client._prepare_payload(sample_request)
        
        assert payload["model"] == "deepseek/deepseek-r1-distill-llama-70b"
        assert len(payload["messages"]) == 2
        assert payload["messages"][0]["role"] == "system"
        assert payload["messages"][0]["content"] == "Test system prompt"
        assert payload["messages"][1]["role"] == "user"
        assert payload["messages"][1]["content"] == "Test prompt"
        assert payload["max_tokens"] == 3000
        assert payload["temperature"] == 0.1
        assert payload["stream"] is False
    
    async def test_prepare_payload_json_mode(self, client, sample_request):
        """Test payload preparation with JSON mode"""
        sample_request.json_mode = True
        payload = await client._prepare_payload(sample_request)
        
        assert payload["response_format"] == {"type": "json_object"}
    
    @patch('aiohttp.ClientSession.post')
    async def test_make_request_success(self, mock_post, client, sample_request, mock_response_data):
        """Test successful API request"""
        # Setup mock response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=mock_response_data)
        mock_post.return_value.__aenter__.return_value = mock_response
        
        # Create session
        client.session = aiohttp.ClientSession()
        
        try:
            headers = await client._prepare_headers(sample_request)
            payload = await client._prepare_payload(sample_request)
            
            result = await client._make_request(sample_request, headers, payload)
            
            assert result == mock_response_data
            mock_post.assert_called_once()
        finally:
            await client.session.close()
    
    @patch('aiohttp.ClientSession.post')
    async def test_make_request_rate_limit(self, mock_post, client, sample_request):
        """Test rate limit handling"""
        # Setup mock response
        mock_response = AsyncMock()
        mock_response.status = 429
        mock_response.headers = {'retry-after': '60'}
        mock_post.return_value.__aenter__.return_value = mock_response
        
        client.session = aiohttp.ClientSession()
        
        try:
            headers = await client._prepare_headers(sample_request)
            payload = await client._prepare_payload(sample_request)
            
            with pytest.raises(RateLimitError) as exc_info:
                await client._make_request(sample_request, headers, payload)
            
            assert exc_info.value.details['retry_after'] == 60
        finally:
            await client.session.close()
    
    @patch('aiohttp.ClientSession.post')
    async def test_make_request_api_error(self, mock_post, client, sample_request):
        """Test API error handling"""
        # Setup mock response
        mock_response = AsyncMock()
        mock_response.status = 400
        mock_response.content_type = 'application/json'
        mock_response.json = AsyncMock(return_value={"error": "Bad request"})
        mock_post.return_value.__aenter__.return_value = mock_response
        
        client.session = aiohttp.ClientSession()
        
        try:
            headers = await client._prepare_headers(sample_request)
            payload = await client._prepare_payload(sample_request)
            
            with pytest.raises(APIError) as exc_info:
                await client._make_request(sample_request, headers, payload)
            
            assert exc_info.value.details['status_code'] == 400
        finally:
            await client.session.close()
    
    def test_parse_response(self, client, sample_request, mock_response_data):
        """Test response parsing"""
        response = client._parse_response(
            sample_request, 
            mock_response_data, 
            1.5, 
            "test-request-id"
        )
        
        assert isinstance(response, LLMResponse)
        assert response.content == "Test response"
        assert response.model_config == DEEPSEEK_R1_CONFIG
        assert response.request_id == "test-request-id"
        assert response.correlation_id == "test-123"
        assert response.agent_id == "test-agent"
        assert response.prompt_tokens == 10
        assert response.completion_tokens == 20
        assert response.total_tokens == 30
        assert response.response_time_seconds == 1.5
        assert response.cost_usd == 0.0  # Free tier
        assert response.finish_reason == "stop"
        assert response.model_name == "deepseek/deepseek-r1-distill-llama-70b"
        assert response.provider == "openrouter"
    
    @patch('aiohttp.ClientSession.post')
    async def test_generate_success(self, mock_post, client, sample_request, mock_response_data):
        """Test successful generation"""
        # Setup mock response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=mock_response_data)
        mock_post.return_value.__aenter__.return_value = mock_response
        
        async with client:
            response = await client.generate(sample_request)
            
            assert isinstance(response, LLMResponse)
            assert response.content == "Test response"
            assert response.total_tokens == 30
    
    async def test_rate_limiting(self, client, sample_request):
        """Test rate limiting functionality"""
        # Fill up rate limit
        for _ in range(DEEPSEEK_R1_CONFIG.rate_limit_rpm):
            client._record_request(DEEPSEEK_R1_CONFIG, 100)
        
        # Next request should be rate limited
        with pytest.raises(RateLimitError):
            await client._check_rate_limits(DEEPSEEK_R1_CONFIG)
    
    def test_circuit_breaker(self, client):
        """Test circuit breaker functionality"""
        # Record failures
        for _ in range(5):
            client._record_failure()
        
        assert client._is_circuit_breaker_open()
        
        # Record success should reset
        client._record_success()
        assert not client._is_circuit_breaker_open()


class TestGroqClient:
    """Test Groq client implementation"""
    
    @pytest.fixture
    def client(self):
        """Create Groq client for testing"""
        return GroqClient(api_key="test_key")
    
    @pytest.fixture
    def sample_request(self):
        """Create sample LLM request"""
        return LLMRequest(
            prompt="Test prompt",
            model_config=LLAMA_3_3_70B_CONFIG,
            system_prompt="Test system prompt",
            correlation_id="test-123",
            agent_id="test-agent"
        )
    
    @pytest.fixture
    def mock_response_data(self):
        """Mock Groq API response"""
        return {
            "choices": [{
                "message": {"content": "Test response"},
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 15,
                "completion_tokens": 25,
                "total_tokens": 40
            },
            "model": "llama-3.3-70b-versatile"
        }
    
    async def test_prepare_headers(self, client, sample_request):
        """Test header preparation"""
        headers = await client._prepare_headers(sample_request)
        
        assert headers["Authorization"] == "Bearer test_key"
        assert headers["Content-Type"] == "application/json"
    
    async def test_prepare_payload_with_functions(self, client, sample_request):
        """Test payload preparation with function calling"""
        sample_request.functions = [
            {
                "name": "test_function",
                "description": "Test function",
                "parameters": {"type": "object"}
            }
        ]
        
        payload = await client._prepare_payload(sample_request)
        
        assert "functions" in payload
        assert payload["function_call"] == "auto"
        assert len(payload["functions"]) == 1
    
    def test_parse_response_with_cost(self, client, sample_request, mock_response_data):
        """Test response parsing with cost calculation"""
        response = client._parse_response(
            sample_request,
            mock_response_data,
            2.0,
            "test-request-id"
        )
        
        assert isinstance(response, LLMResponse)
        assert response.content == "Test response"
        assert response.total_tokens == 40
        assert response.cost_usd == 40 * LLAMA_3_3_70B_CONFIG.cost_per_token
        assert response.provider == "groq"


class TestUsageStats:
    """Test usage statistics tracking"""
    
    def test_update_request(self):
        """Test request statistics update"""
        from core.llm.models import UsageStats
        
        stats = UsageStats(provider="test", model_name="test-model")
        
        # Create mock response
        response = LLMResponse(
            content="test",
            model_config=DEEPSEEK_R1_CONFIG,
            request_id="test",
            total_tokens=100,
            cost_usd=0.05,
            response_time_seconds=1.5
        )
        
        stats.update_request(response)
        
        assert stats.requests_today == 1
        assert stats.tokens_today == 100
        assert stats.cost_today == 0.05
        assert stats.avg_response_time == 1.5
    
    def test_update_error(self):
        """Test error statistics update"""
        from core.llm.models import UsageStats
        
        stats = UsageStats(provider="test", model_name="test-model")
        stats.requests_today = 5
        
        stats.update_error()
        
        assert stats.error_count == 1
        assert stats.success_rate == 5/6  # 5 successes out of 6 total
    
    def test_rate_limit_check(self):
        """Test rate limit checking"""
        from core.llm.models import UsageStats
        
        stats = UsageStats(provider="test", model_name="test-model")
        config = ModelConfig(
            name="test",
            provider=ModelProvider.OPENROUTER,
            model_type=ModelType.REASONING,
            rate_limit_rpm=10,
            rate_limit_tpm=1000
        )
        
        # Fill up rate limits
        stats.requests_this_minute = 10
        stats.tokens_this_minute = 1000
        
        assert stats.is_rate_limited(config)
        
        # Reset and check again
        stats.reset_minute()
        assert not stats.is_rate_limited(config)


@pytest.mark.asyncio
async def test_client_context_managers():
    """Test async context manager functionality"""
    openrouter_client = OpenRouterClient(api_key="test")
    groq_client = GroqClient(api_key="test")
    
    # Test context managers
    async with openrouter_client as client:
        assert client.session is not None
    
    async with groq_client as client:
        assert client.session is not None


if __name__ == "__main__":
    pytest.main([__file__])