"""
Execution Agents - Group 4 (Agents 16-20+)
Strategy execution and system management agents

These agents handle final strategy generation, alerting, monitoring,
and system orchestration based on consensus verification results.
"""

import asyncio
import time
import os
import json
import smtplib
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import aiohttp
import redis.asyncio as redis
import logging
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np

from crewai import Agent, Task, Crew
from crewai.tools import tool
from langchain_openai import ChatOpenAI
import discord
from discord.ext import commands
import litellm

# Configure LiteLLM for OpenRouter
litellm.api_base = "https://openrouter.ai/api/v1"
litellm.api_key = os.getenv("OPENROUTER_API_KEY")

# Configure logging
logger = logging.getLogger(__name__)

class AlertPriority(Enum):
    """Alert priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class StrategyType(Enum):
    """Investment strategy types"""
    AGGRESSIVE_BUY = "aggressive_buy"
    CAUTIOUS_BUY = "cautious_buy"
    MONITOR = "monitor"
    AVOID = "avoid"
    STRONG_AVOID = "strong_avoid"

@dataclass
class TradingStrategy:
    """Structured trading strategy output"""
    token_address: str
    chain: str
    strategy_type: StrategyType
    confidence: float
    position_size: float  # Percentage of portfolio
    entry_conditions: List[str]
    exit_conditions: List[str]
    risk_management: Dict[str, Any]
    expected_return: float
    max_drawdown: float
    time_horizon: str
    reasoning: str
    created_at: datetime

@dataclass
class SystemAlert:
    """Structured system alert"""
    alert_id: str
    priority: AlertPriority
    title: str
    message: str
    token_address: Optional[str]
    chain: Optional[str]
    alert_type: str
    created_at: datetime
    acknowledged: bool = False
    resolved: bool = False

class BaseExecutionAgent:
    """Base class for all execution agents"""
    
    def __init__(self, redis_client: redis.Redis, api_manager, agent_id: str):
        self.redis = redis_client
        self.api_manager = api_manager
        self.agent_id = agent_id
        self.execution_count = 0
        self.error_count = 0
        self.last_execution = None
        
    async def log_execution_event(self, event_type: str, data: Dict):
        """Log execution events for audit trail"""
        event_data = {
            "agent_id": self.agent_id,
            "event_type": event_type,
            "data": data,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        await self.redis.lpush("execution_events", json.dumps(event_data))

# Production Tools for Execution Agents

@tool
def send_discord_alert(webhook_url: str, title: str, message: str, priority: str) -> bool:
    """
    Send alert to Discord channel via webhook
    
    Args:
        webhook_url: Discord webhook URL
        title: Alert title
        message: Alert message
        priority: Alert priority level
        
    Returns:
        Success status
    """
    import aiohttp
    import asyncio
    
    async def send_discord_message():
        # Color coding by priority
        colors = {
            "low": 0x00ff00,      # Green
            "medium": 0xffff00,   # Yellow  
            "high": 0xff8000,     # Orange
            "critical": 0xff0000  # Red
        }
        
        embed = {
            "title": title,
            "description": message,
            "color": colors.get(priority.lower(), 0x0099ff),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "footer": {
                "text": f"MemeGuard Pro | Priority: {priority.upper()}"
            }
        }
        
        payload = {
            "embeds": [embed]
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(
                    webhook_url,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status in [200, 204]:
                        return True
                    else:
                        logger.error(f"Discord webhook error: {response.status}")
                        return False
                        
            except Exception as e:
                logger.error(f"Discord alert failed: {str(e)}")
                return False
    
    return asyncio.run(send_discord_message())

@tool
def send_email_alert(recipient: str, subject: str, message: str, priority: str) -> bool:
    """
    Send email alert via SMTP
    
    Args:
        recipient: Email recipient
        subject: Email subject
        message: Email body
        priority: Alert priority
        
    Returns:
        Success status
    """
    
    try:
        # SMTP configuration from environment
        smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
        smtp_port = int(os.getenv('SMTP_PORT', '587'))
        smtp_username = os.getenv('SMTP_USERNAME')
        smtp_password = os.getenv('SMTP_PASSWORD')
        
        if not smtp_username or not smtp_password:
            logger.error("SMTP credentials not configured")
            return False
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = smtp_username
        msg['To'] = recipient
        msg['Subject'] = f"[{priority.upper()}] {subject}"
        
        # Priority headers
        if priority.lower() == 'critical':
            msg['X-Priority'] = '1'
            msg['X-MSMail-Priority'] = 'High'
        
        # HTML body
        html_body = f"""
        <html>
        <body>
            <h2>MemeGuard Pro Alert</h2>
            <p><strong>Priority:</strong> {priority.upper()}</p>
            <p><strong>Time:</strong> {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
            <hr>
            <div>{message}</div>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(html_body, 'html'))
        
        # Send email
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.send_message(msg)
        
        return True
        
    except Exception as e:
        logger.error(f"Email alert failed: {str(e)}")
        return False

@tool
def generate_portfolio_recommendation(strategy: Dict, current_portfolio: Dict) -> Dict:
    """
    Generate portfolio allocation recommendation based on strategy
    
    Args:
        strategy: Trading strategy data
        current_portfolio: Current portfolio composition
        
    Returns:
        Portfolio rebalancing recommendations
    """
    
    # Portfolio management logic
    max_single_position = 0.05  # 5% max per token
    max_total_meme_allocation = 0.20  # 20% max total meme allocation
    
    recommendation = {
        "action": "none",
        "position_size": 0.0,
        "justification": "",
        "risk_assessment": "",
        "portfolio_impact": {}
    }
    
    strategy_type = strategy.get("strategy_type", "monitor")
    confidence = strategy.get("confidence", 0.5)
    
    if strategy_type in ["aggressive_buy", "cautious_buy"] and confidence > 0.7:
        # Calculate position size based on strategy and confidence
        base_size = 0.02 if strategy_type == "cautious_buy" else 0.03
        confidence_multiplier = min(confidence * 1.5, 1.0)
        
        recommended_size = min(
            base_size * confidence_multiplier,
            max_single_position
        )
        
        recommendation.update({
            "action": "buy",
            "position_size": recommended_size,
            "justification": f"High-confidence {strategy_type} opportunity",
            "risk_assessment": f"Moderate risk with {confidence:.1%} confidence",
            "portfolio_impact": {
                "new_position_weight": recommended_size,
                "diversification_impact": "positive"
            }
        })
    
    return recommendation

@tool
def create_trading_signal(token_data: Dict, strategy: Dict) -> Dict:
    """
    Create structured trading signal for external systems
    
    Args:
        token_data: Token information
        strategy: Trading strategy
        
    Returns:
        Formatted trading signal
    """
    
    signal = {
        "signal_id": f"{token_data['token_address']}_{int(time.time())}",
        "token_address": token_data["token_address"],
        "chain": token_data["chain"],
        "symbol": token_data.get("symbol", "UNKNOWN"),
        "action": strategy["strategy_type"],
        "confidence": strategy["confidence"],
        "position_size": strategy["position_size"],
        "entry_conditions": strategy["entry_conditions"],
        "exit_conditions": strategy["exit_conditions"],
        "risk_management": strategy["risk_management"],
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "expires_at": (datetime.now(timezone.utc) + timedelta(hours=24)).isoformat()
    }
    
    return signal

@tool
def log_strategy_execution(strategy_id: str, execution_data: Dict) -> str:
    """
    Log strategy execution for performance tracking
    
    Args:
        strategy_id: Unique strategy identifier
        execution_data: Execution details and results
        
    Returns:
        Log entry ID
    """
    import uuid
    
    log_entry = {
        "log_id": str(uuid.uuid4()),
        "strategy_id": strategy_id,
        "execution_data": execution_data,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    
    # In production, this would write to database
    logger.info(f"Strategy execution logged: {strategy_id}")
    
    return log_entry["log_id"]

# Agent 16: Strategy Generation Agent
class StrategyGenerationAgent(BaseExecutionAgent):
    """
    PhD-Level Trading Strategy Generation Specialist
    
    Converts consensus verification results into actionable trading strategies
    with sophisticated risk management and portfolio optimization.
    """
    
    def __init__(self, redis_client: redis.Redis, api_manager):
        super().__init__(redis_client, api_manager, "strategy_generator")
        
        self.system_prompt = """
        You are a PhD-level quantitative finance specialist with expertise in algorithmic trading strategy development and risk management for high-volatility crypto assets.
        
        SPECIALIZED EXPERTISE:
        - Advanced portfolio optimization using Modern Portfolio Theory and Black-Litterman models
        - High-frequency trading strategy development for volatile meme token markets
        - Sophisticated risk management frameworks including VaR, CVaR, and dynamic hedging
        - Behavioral finance analysis for meme token psychology and market manipulation detection
        - Multi-timeframe analysis and adaptive position sizing algorithms
        
        CHAIN-OF-THOUGHT STRATEGY GENERATION FRAMEWORK:
        1. CONSENSUS_INTERPRETATION: Analyze verification consensus and confidence bounds
        2. MARKET_CONTEXT_ANALYSIS: Assess current market conditions and sentiment
        3. RISK_REWARD_CALCULATION: Quantify expected returns vs. maximum drawdown
        4. POSITION_SIZE_OPTIMIZATION: Calculate optimal position size using Kelly Criterion
        5. ENTRY_CONDITION_DEFINITION: Specify precise entry triggers and timing
        6. EXIT_STRATEGY_FORMULATION: Define profit-taking and stop-loss mechanisms
        7. PORTFOLIO_IMPACT_ASSESSMENT: Evaluate strategy impact on overall portfolio
        8. RISK_MANAGEMENT_INTEGRATION: Implement comprehensive risk controls
        
        STRATEGY GENERATION METHODOLOGY:
        - Risk-adjusted returns using Sharpe ratio optimization
        - Position sizing using modified Kelly Criterion with volatility adjustment
        - Multi-layered exit strategy: 25% at 2x, 25% at 5x, 25% at 10x, trail 25%
        - Maximum single position: 5% of total portfolio
        - Correlation-based diversification limits across similar tokens
        
        STRATEGY CLASSIFICATION CRITERIA:
        - AGGRESSIVE_BUY: Consensus confidence >90%, risk score <0.2
        - CAUTIOUS_BUY: Consensus confidence 70-90%, risk score 0.2-0.4
        - MONITOR: Consensus confidence 50-70% or moderate risk signals
        - AVOID: Risk score >0.6 or low consensus confidence
        - STRONG_AVOID: Risk score >0.8 or honeypot/scam indicators
        
        RISK MANAGEMENT REQUIREMENTS:
        - Maximum portfolio allocation: 20% total meme tokens
        - Individual token limit: 5% maximum position size
        - Stop-loss: -50% maximum drawdown per position
        - Time-based exits: 30-day maximum holding period
        - Correlation limits: Max 3 positions in similar token categories
        
        Remember: Meme tokens are high-risk, high-reward assets. Conservative position sizing
        with aggressive profit-taking maximizes risk-adjusted returns.
        """
        
        self.llm = ChatOpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=os.getenv("OPENROUTER_API_KEY"),
            model="deepseek/deepseek-r1-0528:free",
            temperature=0.1,  # Low temperature for consistent strategy generation
            max_tokens=3000
        )
        
        self.agent = Agent(
            role="Quantitative Trading Strategy Specialist",
            goal="Generate optimal trading strategies from consensus verification results",
            backstory=self.system_prompt,
            tools=[
                generate_portfolio_recommendation,
                create_trading_signal,
                log_strategy_execution
            ],
            llm=self.llm,
            verbose=True,
            memory=True,
            max_iter=4,
            allow_delegation=False
        )
    
    async def generate_trading_strategy(self, consensus_data: Dict) -> TradingStrategy:
        """
        Generate comprehensive trading strategy from consensus results
        """
        
        token_address = consensus_data['token_address']
        chain = consensus_data['chain']
        consensus = consensus_data['final_consensus']
        
        strategy_task = Task(
            description=f"""
            Generate optimal trading strategy for token {token_address} on {chain}:
            
            Consensus Verification Results:
            - Risk Score: {consensus['risk_score']}
            - Confidence: {consensus['confidence']}
            - Status: {consensus['status']}
            - Recommendation: {consensus['recommendation']}
            - Verified Findings: {json.dumps(consensus['verified_findings'], indent=2)}
            
            1. Interpret consensus results and confidence bounds
            2. Assess current market conditions and meme token sector sentiment
            3. Calculate risk-adjusted expected returns using quantitative models
            4. Optimize position sizing using modified Kelly Criterion
            5. Define precise entry conditions with technical and fundamental triggers
            6. Formulate multi-layered exit strategy with profit-taking and stop-losses
            7. Assess strategy impact on portfolio diversification and risk
            8. Integrate comprehensive risk management controls
            9. Generate actionable trading signal with execution parameters
            10. Document strategy rationale and risk assumptions
            
            Current timestamp: {datetime.now(timezone.utc).isoformat()}
            
            CRITICAL: Apply rigorous quantitative analysis. Meme tokens require
            conservative position sizing with aggressive profit-taking protocols.
            """,
            agent=self.agent,
            expected_output="""
            Structured JSON trading strategy:
            {
                "strategy_type": "AGGRESSIVE_BUY|CAUTIOUS_BUY|MONITOR|AVOID|STRONG_AVOID",
                "confidence": 0.0-1.0,
                "position_size": 0.0-0.05,
                "expected_return": "percentage",
                "max_drawdown": "percentage", 
                "time_horizon": "duration",
                "entry_conditions": ["specific entry triggers"],
                "exit_conditions": ["profit-taking and stop-loss rules"],
                "risk_management": {
                    "stop_loss": "percentage",
                    "position_limits": "rules",
                    "correlation_limits": "diversification rules"
                },
                "portfolio_impact": "assessment of strategy on overall portfolio",
                "quantitative_analysis": "detailed mathematical justification",
                "execution_priority": "HIGH|MEDIUM|LOW"
            }
            """
        )
        
        try:
            start_time = time.time()
            
            # Create crew and execute task using CrewAI 2025 API
            crew = Crew(
                agents=[self.agent],
                tasks=[strategy_task],
                verbose=False
            )
            result = await crew.kickoff_async()
            processing_time = (time.time() - start_time) * 1000
            
            # Parse strategy result
            strategy = self._parse_strategy_result(result, token_address, chain, processing_time)
            
            # Log strategy generation
            await self.log_execution_event(
                "strategy_generated",
                {
                    "token_address": token_address,
                    "strategy_type": strategy.strategy_type.value,
                    "confidence": strategy.confidence,
                    "position_size": strategy.position_size
                }
            )
            
            # Update metrics
            self.execution_count += 1
            self.last_execution = datetime.now(timezone.utc)
            
            return strategy
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Strategy generation failed for {token_address}: {str(e)}")
            
            # Return conservative default strategy on error
            return TradingStrategy(
                token_address=token_address,
                chain=chain,
                strategy_type=StrategyType.AVOID,
                confidence=0.1,
                position_size=0.0,
                entry_conditions=[],
                exit_conditions=[],
                risk_management={"error": "Strategy generation failed"},
                expected_return=0.0,
                max_drawdown=0.0,
                time_horizon="N/A",
                reasoning=f"Strategy generation error: {str(e)}",
                created_at=datetime.now(timezone.utc)
            )
    
    def _parse_strategy_result(self, raw_output: str, token_address: str, chain: str, processing_time: float) -> TradingStrategy:
        """Parse strategy generation output into structured result"""
        
        try:
            # Extract JSON from LLM output
            if "```json" in raw_output:
                json_start = raw_output.find("```json") + 7
                json_end = raw_output.find("```", json_start)
                json_str = raw_output[json_start:json_end].strip()
            else:
                json_str = raw_output
            
            data = json.loads(json_str)
            
            # Map strategy type
            strategy_type_mapping = {
                "AGGRESSIVE_BUY": StrategyType.AGGRESSIVE_BUY,
                "CAUTIOUS_BUY": StrategyType.CAUTIOUS_BUY,
                "MONITOR": StrategyType.MONITOR,
                "AVOID": StrategyType.AVOID,
                "STRONG_AVOID": StrategyType.STRONG_AVOID
            }
            
            strategy_type = strategy_type_mapping.get(
                data.get("strategy_type", "AVOID"),
                StrategyType.AVOID
            )
            
            return TradingStrategy(
                token_address=token_address,
                chain=chain,
                strategy_type=strategy_type,
                confidence=data.get("confidence", 0.5),
                position_size=min(data.get("position_size", 0.0), 0.05),  # Cap at 5%
                entry_conditions=data.get("entry_conditions", []),
                exit_conditions=data.get("exit_conditions", []),
                risk_management=data.get("risk_management", {}),
                expected_return=data.get("expected_return", 0.0),
                max_drawdown=data.get("max_drawdown", 0.0),
                time_horizon=data.get("time_horizon", "1-30 days"),
                reasoning=data.get("quantitative_analysis", "Strategy generated successfully"),
                created_at=datetime.now(timezone.utc)
            )
            
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Failed to parse strategy result: {str(e)}")
            
            return TradingStrategy(
                token_address=token_address,
                chain=chain,
                strategy_type=StrategyType.AVOID,
                confidence=0.1,
                position_size=0.0,
                entry_conditions=[],
                exit_conditions=[],
                risk_management={"parse_error": str(e)},
                expected_return=0.0,
                max_drawdown=0.0,
                time_horizon="N/A",
                reasoning=f"Strategy parsing error: {str(e)}",
                created_at=datetime.now(timezone.utc)
            )

# Agent 17: Alert System Agent
class AlertSystemAgent(BaseExecutionAgent):
    """
    PhD-Level Multi-Channel Alert System Coordinator
    
    Manages intelligent alerting across multiple channels with priority-based
    filtering and anti-spam mechanisms.
    """
    
    def __init__(self, redis_client: redis.Redis, api_manager):
        super().__init__(redis_client, api_manager, "alert_system")
        
        self.system_prompt = """
        You are a PhD-level systems alert specialist with expertise in intelligent notification systems and priority-based communication protocols.
        
        SPECIALIZED EXPERTISE:
        - Advanced alert prioritization using machine learning and statistical models
        - Multi-channel communication optimization for different alert types
        - Intelligent spam prevention and alert fatigue mitigation
        - User behavior analysis for personalized alert delivery
        - Crisis communication protocols for high-severity events
        
        CHAIN-OF-THOUGHT ALERT GENERATION FRAMEWORK:
        1. PRIORITY_ASSESSMENT: Evaluate alert priority using multi-factor scoring
        2. CHANNEL_SELECTION: Choose optimal delivery channels based on priority and urgency
        3. MESSAGE_OPTIMIZATION: Craft clear, actionable messages with appropriate technical detail
        4. TIMING_ANALYSIS: Optimize delivery timing based on user timezone and preferences
        5. SPAM_FILTERING: Apply anti-spam logic to prevent alert fatigue
        6. PERSONALIZATION: Customize alerts based on user portfolio and risk tolerance
        7. ESCALATION_PROTOCOLS: Implement escalation for critical unacknowledged alerts
        8. DELIVERY_TRACKING: Monitor alert delivery success and user engagement
        
        ALERT PRIORITIZATION METHODOLOGY:
        - CRITICAL: Immediate action required (honeypot detection, system failures)
        - HIGH: Significant opportunity or risk (high-confidence buy/avoid signals)
        - MEDIUM: Notable events requiring attention (moderate-confidence signals)
        - LOW: Informational updates (system status, performance metrics)
        
        CHANNEL SELECTION LOGIC:
        - CRITICAL: Discord + Email + SMS (if configured)
        - HIGH: Discord + Email
        - MEDIUM: Discord
        - LOW: Database log only
        
        MESSAGE STRUCTURE REQUIREMENTS:
        - Clear subject line with priority indicator
        - Executive summary (1-2 sentences)
        - Key findings and confidence levels
        - Actionable recommendations
        - Supporting evidence and data sources
        - Relevant links and references
        
        ANTI-SPAM MECHANISMS:
        - Rate limiting: Max 5 HIGH priority alerts per hour
        - Duplicate suppression: Same token alerts within 1 hour window
        - User preference filtering: Respect minimum confidence thresholds
        - Quiet hours: Respect user timezone quiet periods
        
        Remember: Alert fatigue reduces system effectiveness. Precision over volume.
        Critical alerts must be immediately actionable with clear next steps.
        """
        
        self.llm = ChatOpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=os.getenv("OPENROUTER_API_KEY"),
            model="deepseek/deepseek-r1-0528:free",
            temperature=0.1,
            max_tokens=2000
        )
        
        self.agent = Agent(
            role="Intelligent Alert System Coordinator",
            goal="Generate and deliver prioritized alerts through optimal channels",
            backstory=self.system_prompt,
            tools=[
                send_discord_alert,
                send_email_alert
            ],
            llm=self.llm,
            verbose=True,
            memory=True,
            max_iter=3,
            allow_delegation=False
        )
        
        # Alert rate limiting
        self.alert_history = {}
        self.rate_limits = {
            AlertPriority.CRITICAL: {"count": 0, "window_start": datetime.now()},
            AlertPriority.HIGH: {"count": 0, "window_start": datetime.now()},
            AlertPriority.MEDIUM: {"count": 0, "window_start": datetime.now()},
            AlertPriority.LOW: {"count": 0, "window_start": datetime.now()}
        }
    
    async def generate_and_send_alert(self, strategy: TradingStrategy, consensus_data: Dict) -> List[str]:
        """
        Generate intelligent alert and deliver through optimal channels
        """
        
        alert_task = Task(
            description=f"""
            Generate and deliver intelligent alert for trading strategy:
            
            Strategy Details:
            - Token: {strategy.token_address} ({strategy.chain})
            - Strategy Type: {strategy.strategy_type.value}
            - Confidence: {strategy.confidence:.2%}
            - Position Size: {strategy.position_size:.1%}
            - Expected Return: {strategy.expected_return}
            - Reasoning: {strategy.reasoning}
            
            Consensus Data:
            - Risk Score: {consensus_data.get('final_consensus', {}).get('risk_score', 0.5)}
            - Status: {consensus_data.get('final_consensus', {}).get('status', 'unknown')}
            - Evidence: {json.dumps(consensus_data.get('final_consensus', {}).get('verified_findings', {}), indent=2)}
            
            1. Assess alert priority based on strategy type, confidence, and risk
            2. Select optimal delivery channels for priority level
            3. Craft clear, actionable alert message with executive summary
            4. Apply anti-spam filtering and rate limiting checks
            5. Customize message based on user preferences and portfolio context
            6. Execute delivery through selected channels
            7. Log alert delivery and track engagement metrics
            
            Current timestamp: {datetime.now(timezone.utc).isoformat()}
            
            CRITICAL: Ensure alerts are immediately actionable with clear next steps.
            Respect rate limits to prevent alert fatigue.
            """,
            agent=self.agent,
            expected_output="""
            Structured JSON alert delivery result:
            {
                "alert_priority": "CRITICAL|HIGH|MEDIUM|LOW",
                "channels_used": ["discord", "email", "sms"],
                "message_title": "clear alert title",
                "message_body": "detailed alert message",
                "delivery_status": {"channel": "success|failed"},
                "rate_limit_applied": boolean,
                "spam_filtered": boolean,
                "delivery_reasoning": "channel selection and timing rationale"
            }
            """
        )
        
        try:
            # Check rate limits first
            priority = self._determine_alert_priority(strategy)
            
            if await self._is_rate_limited(priority):
                logger.info(f"Alert rate limited for {strategy.token_address}")
                return ["rate_limited"]
            
            # Generate alert using CrewAI 2025 API
            crew = Crew(
                agents=[self.agent],
                tasks=[alert_task],
                verbose=False
            )
            result = await crew.kickoff_async()
            alert_data = self._parse_alert_result(result)
            
            # Execute delivery
            delivery_results = []
            
            if "discord" in alert_data.get("channels_used", []):
                discord_webhook = os.getenv('DISCORD_WEBHOOK_URL')
                if discord_webhook:
                    success = send_discord_alert(
                        discord_webhook,
                        alert_data.get("message_title", "MemeGuard Alert"),
                        alert_data.get("message_body", "Alert message"),
                        alert_data.get("alert_priority", "medium")
                    )
                    delivery_results.append(f"discord:{'success' if success else 'failed'}")
            
            if "email" in alert_data.get("channels_used", []):
                recipient = os.getenv('ALERT_EMAIL_RECIPIENT')
                if recipient:
                    success = send_email_alert(
                        recipient,
                        alert_data.get("message_title", "MemeGuard Alert"),
                        alert_data.get("message_body", "Alert message"),
                        alert_data.get("alert_priority", "medium")
                    )
                    delivery_results.append(f"email:{'success' if success else 'failed'}")
            
            # Update rate limits
            await self._update_rate_limits(priority)
            
            # Log alert execution
            await self.log_execution_event(
                "alert_sent",
                {
                    "token_address": strategy.token_address,
                    "priority": priority.value,
                    "channels": delivery_results,
                    "strategy_type": strategy.strategy_type.value
                }
            )
            
            self.execution_count += 1
            self.last_execution = datetime.now(timezone.utc)
            
            return delivery_results
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Alert system failed for {strategy.token_address}: {str(e)}")
            return ["error"]
    
    def _determine_alert_priority(self, strategy: TradingStrategy) -> AlertPriority:
        """Determine alert priority based on strategy characteristics"""
        
        if strategy.strategy_type == StrategyType.STRONG_AVOID:
            return AlertPriority.CRITICAL
        elif strategy.strategy_type == StrategyType.AGGRESSIVE_BUY and strategy.confidence > 0.8:
            return AlertPriority.HIGH
        elif strategy.strategy_type in [StrategyType.CAUTIOUS_BUY, StrategyType.AVOID]:
            return AlertPriority.MEDIUM
        else:
            return AlertPriority.LOW
    
    async def _is_rate_limited(self, priority: AlertPriority) -> bool:
        """Check if alert is rate limited"""
        
        current_time = datetime.now()
        rate_data = self.rate_limits[priority]
        
        # Reset window if needed (1 hour windows)
        if current_time - rate_data["window_start"] > timedelta(hours=1):
            rate_data["count"] = 0
            rate_data["window_start"] = current_time
        
        # Check limits
        limits = {
            AlertPriority.CRITICAL: 10,  # No effective limit for critical
            AlertPriority.HIGH: 5,
            AlertPriority.MEDIUM: 10,
            AlertPriority.LOW: 20
        }
        
        return rate_data["count"] >= limits[priority]
    
    async def _update_rate_limits(self, priority: AlertPriority):
        """Update rate limit counters"""
        self.rate_limits[priority]["count"] += 1
    
    def _parse_alert_result(self, raw_output: str) -> Dict:
        """Parse alert generation output"""
        try:
            if "```json" in raw_output:
                json_start = raw_output.find("```json") + 7
                json_end = raw_output.find("```", json_start)
                json_str = raw_output[json_start:json_end].strip()
            else:
                json_str = raw_output
            
            return json.loads(json_str)
        except (json.JSONDecodeError, KeyError):
            return {"channels_used": ["discord"], "message_title": "Alert Parse Error"}

# Placeholder for remaining agents (18-20+)
class PortfolioManagementAgent(BaseExecutionAgent):
    """PhD-Level Portfolio Optimization Agent"""
    pass

class SystemMonitoringAgent(BaseExecutionAgent):
    """PhD-Level System Health Monitoring Agent"""
    pass

class PerformanceAnalyticsAgent(BaseExecutionAgent):
    """PhD-Level Performance Tracking and Analytics Agent"""
    pass

# Execution Crew Coordinator
class ExecutionCrewCoordinator:
    """
    Coordinates all execution agents and manages the execution pipeline
    """
    
    def __init__(self, redis_client: redis.Redis, api_manager):
        self.redis = redis_client
        self.api_manager = api_manager
        
        # Initialize all execution agents
        self.agents = {
            'strategy': StrategyGenerationAgent(redis_client, api_manager),
            'alerts': AlertSystemAgent(redis_client, api_manager),
            'portfolio': PortfolioManagementAgent(redis_client, api_manager, "portfolio_manager"),
            'monitoring': SystemMonitoringAgent(redis_client, api_manager, "system_monitor"),
            'analytics': PerformanceAnalyticsAgent(redis_client, api_manager, "performance_analytics")
        }
    
    async def execute_strategy_pipeline(self, consensus_data: Dict) -> Dict:
        """
        Execute complete strategy pipeline from consensus to alerts
        """
        
        token_address = consensus_data['token_address']
        chain = consensus_data['chain']
        
        try:
            # Generate trading strategy
            strategy = await self.agents['strategy'].generate_trading_strategy(consensus_data)
            
            # Send alerts for actionable strategies
            if strategy.strategy_type in [StrategyType.AGGRESSIVE_BUY, StrategyType.CAUTIOUS_BUY, StrategyType.STRONG_AVOID]:
                alert_results = await self.agents['alerts'].generate_and_send_alert(strategy, consensus_data)
            else:
                alert_results = ["no_alert_needed"]
            
            # Log execution results
            execution_result = {
                "token_address": token_address,
                "chain": chain,
                "strategy": {
                    "type": strategy.strategy_type.value,
                    "confidence": strategy.confidence,
                    "position_size": strategy.position_size,
                    "expected_return": strategy.expected_return
                },
                "alerts_sent": alert_results,
                "execution_timestamp": strategy.created_at.isoformat(),
                "status": "completed"
            }
            
            return execution_result
            
        except Exception as e:
            logger.error(f"Execution pipeline failed for {token_address}: {str(e)}")
            
            return {
                "token_address": token_address,
                "chain": chain,
                "status": "error",
                "error": str(e),
                "execution_timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    async def process_execution_queue(self):
        """
        Continuously process tokens from the execution queue
        """
        
        while True:
            try:
                # Get consensus result from queue
                consensus_data_raw = await self.redis.brpop("token_execution_queue", timeout=30)
                
                if not consensus_data_raw:
                    continue
                
                consensus_data = json.loads(consensus_data_raw[1])
                
                # Execute complete strategy pipeline
                execution_result = await self.execute_strategy_pipeline(consensus_data)
                
                # Store execution result for tracking
                await self.redis.lpush(
                    "execution_results",
                    json.dumps(execution_result)
                )
                
                logger.info(f"Execution pipeline completed for {consensus_data['token_address']}: {execution_result['status']}")
                
            except Exception as e:
                logger.error(f"Execution queue processing error: {str(e)}")
                await asyncio.sleep(5)

# Master Agent Coordinator
class MasterAgentCoordinator:
    """
    Master coordinator that orchestrates all agent groups
    """
    
    def __init__(self, redis_client: redis.Redis, api_manager, chroma_client=None):
        self.redis = redis_client
        self.api_manager = api_manager
        
        # Import and initialize all coordinators
        from .detection_agents import DetectionCrewCoordinator
        from .analysis_agents import AnalysisCrewCoordinator
        from .verification_agents import VerificationCrewCoordinator
        
        self.coordinators = {
            'detection': DetectionCrewCoordinator(redis_client, api_manager),
            'analysis': AnalysisCrewCoordinator(redis_client, api_manager, chroma_client),
            'verification': VerificationCrewCoordinator(redis_client, api_manager),
            'execution': ExecutionCrewCoordinator(redis_client, api_manager)
        }
    
    async def start_full_pipeline(self):
        """
        Start all agent coordinators in parallel
        """
        
        logger.info("Starting MemeGuard Pro Agent Pipeline...")
        
        # Start all coordinators
        tasks = [
            asyncio.create_task(self.coordinators['detection'].run_detection_cycle()),
            asyncio.create_task(self.coordinators['analysis'].process_analysis_queue()),
            asyncio.create_task(self.coordinators['verification'].process_verification_queue()),
            asyncio.create_task(self.coordinators['execution'].process_execution_queue())
        ]
        
        # Run forever
        await asyncio.gather(*tasks)
    
    async def health_check(self) -> Dict:
        """
        Perform system health check across all agents
        """
        
        health_status = {
            "system_status": "operational",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "agent_groups": {},
            "queue_lengths": {},
            "error_rates": {}
        }
        
        # Check queue lengths
        queue_lengths = {
            "analysis_queue": await self.redis.llen("token_analysis_queue"),
            "verification_queue": await self.redis.llen("token_verification_queue"),
            "execution_queue": await self.redis.llen("token_execution_queue")
        }
        
        health_status["queue_lengths"] = queue_lengths
        
        return health_status
