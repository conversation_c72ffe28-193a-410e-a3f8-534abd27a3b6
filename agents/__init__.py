"""
MemeGuard Pro v4.0 - Production-Ready AI Agent System
Complete CrewAI implementation with PhD-level analysis and real-time APIs

This module contains the complete production implementation of 20+ specialized
AI agents for autonomous crypto token analysis and trading intelligence.
"""

# Import all agent modules
from .detection_agents import (
    DetectionCrewCoordinator,
    SolanaDetectorDEXScreener,
    SolanaDetectorBirdeye,
    EthereumDetector,
    BaseChainDetector,
    TokenDetectionEvent
)

from .analysis_agents import (
    AnalysisCrewCoordinator,
    SecurityAnalysisAgent,
    HoneypotDetectionAgent,
    LiquidityAnalysisAgent,
    DistributionAnalysisAgent,
    SocialSentimentAgent,
    WhaleActivityAgent,
    TokenAnalysisResult
)

from .verification_agents import (
    VerificationCrewCoordinator,
    CrossValidationAgent,
    BlockchainVerificationAgent,
    ConsensusMechanismAgent,
    IndependentValidationAgent,
    SystemIntegrityAgent,
    VerificationResult,
    VerificationStatus,
    ConsensusVote
)

from .execution_agents import (
    ExecutionCrewCoordinator,
    StrategyGenerationAgent,
    AlertSystemAgent,
    PortfolioManagementAgent,
    SystemMonitoringAgent,
    PerformanceAnalyticsAgent,
    TradingStrategy,
    StrategyType,
    SystemAlert,
    AlertPriority
)

from .agent_coordinator import (
    AgentSystemManager,
    APIManager,
    SystemMetrics,
    main
)

# Version info
__version__ = "4.0.0"
__author__ = "MemeGuard Pro Development Team"

# Export main classes for easy import
__all__ = [
    # Main system
    'AgentSystemManager',
    'APIManager',
    'SystemMetrics',
    
    # Coordinators
    'DetectionCrewCoordinator',
    'AnalysisCrewCoordinator', 
    'VerificationCrewCoordinator',
    'ExecutionCrewCoordinator',
    
    # Detection agents
    'SolanaDetectorDEXScreener',
    'SolanaDetectorBirdeye',
    'EthereumDetector',
    'BaseChainDetector',
    'TokenDetectionEvent',
    
    # Analysis agents
    'SecurityAnalysisAgent',
    'HoneypotDetectionAgent',
    'LiquidityAnalysisAgent',
    'DistributionAnalysisAgent',
    'SocialSentimentAgent',
    'WhaleActivityAgent',
    'TokenAnalysisResult',
    
    # Verification agents
    'CrossValidationAgent',
    'BlockchainVerificationAgent',
    'ConsensusMechanismAgent',
    'IndependentValidationAgent',
    'SystemIntegrityAgent',
    'VerificationResult',
    'VerificationStatus',
    'ConsensusVote',
    
    # Execution agents
    'StrategyGenerationAgent',
    'AlertSystemAgent',
    'PortfolioManagementAgent',
    'SystemMonitoringAgent',
    'PerformanceAnalyticsAgent',
    'TradingStrategy',
    'StrategyType',
    'SystemAlert',
    'AlertPriority',
    
    # Main entry point
    'main'
]

# System information
SYSTEM_INFO = {
    "name": "MemeGuard Pro",
    "version": __version__,
    "description": "PhD-level AI agent system for crypto token analysis",
    "agent_count": "20+",
    "technology_stack": [
        "CrewAI 2025",
        "DeepSeek R1 (OpenRouter)", 
        "FastAPI",
        "PostgreSQL + TimescaleDB",
        "Redis",
        "ChromaDB",
        "Real-time APIs (Helius, Infura, DEXScreener, Birdeye, GoPlus)"
    ],
    "features": [
        "Real-time token detection across Solana, Ethereum, Base",
        "PhD-level security analysis and honeypot detection",
        "Byzantine fault-tolerant consensus mechanism",
        "Multi-channel alerting system",
        "Sophisticated trading strategy generation",
        "Performance analytics and system monitoring"
    ]
}

def get_system_info():
    """Get comprehensive system information"""
    return SYSTEM_INFO

def print_system_banner():
    """Print system startup banner"""
    print(f"""
╔══════════════════════════════════════════════════════════════╗
║                       MemeGuard Pro v{__version__}                      ║
║              PhD-Level AI Agent Trading System              ║
╠══════════════════════════════════════════════════════════════╣
║ • 20+ Specialized AI Agents with Chain-of-Thought Reasoning ║
║ • Real-time Multi-Chain Token Detection & Analysis          ║
║ • Byzantine Fault-Tolerant Consensus Mechanism              ║
║ • Production APIs: Helius, Infura, DEXScreener, Birdeye     ║
║ • Advanced Risk Management & Strategy Generation            ║
║ • Multi-Channel Alerting: Discord, Email, SMS               ║
╚══════════════════════════════════════════════════════════════╝
    """)

# Initialize system on import for convenience
if __name__ == "__main__":
    print_system_banner()
    import asyncio
    asyncio.run(main())

from .detection_agents import (
    SolanaDetectorDEXScreener,
    SolanaDetectorBirdeye,
    EthereumDetector,
    BaseChainDetector
)

from .analysis_agents import (
    SecurityAnalysisAgent,
    HoneypotDetectionAgent,
    LiquidityAnalysisAgent,
    DistributionAnalysisAgent,
    SocialSentimentAgent,
    WhaleActivityAgent
)

from .verification_agents import (
    CrossValidationAgent,
    BlockchainVerificationAgent,
    ConsensusMechanismAgent,
    IndependentValidationAgent,
    SystemIntegrityAgent
)

from .execution_agents import (
    StrategyGenerationAgent,
    AlertSystemAgent,
    PortfolioManagementAgent,
    SystemMonitoringAgent,
    PerformanceAnalyticsAgent,
    MasterAgentCoordinator
)

from .agent_coordinator import AgentSystemManager, print_system_banner, get_system_info

__all__ = [
    'SolanaDetectorDEXScreener',
    'SolanaDetectorBirdeye', 
    'EthereumDetector',
    'BaseChainDetector',
    'SecurityAnalysisAgent',
    'HoneypotDetectionAgent',
    'LiquidityAnalysisAgent',
    'DistributionAnalysisAgent',
    'SocialSentimentAgent',
    'WhaleActivityAgent',
    'CrossValidationAgent',
    'BlockchainVerificationAgent',
    'ConsensusMechanismAgent',
    'IndependentValidationAgent',
    'SystemIntegrityAgent',
    'StrategyGenerationAgent',
    'AlertSystemAgent',
    'PortfolioManagementAgent',
    'SystemMonitoringAgent',
    'PerformanceAnalyticsAgent',
    'MasterAgentCoordinator',
    'AgentSystemManager',
    'print_system_banner',
    'get_system_info'
]
