#!/usr/bin/env python3
"""
Agent Tool Execution Test - Senior Engineer Debug
Isolating and fixing the CrewAI tool execution issue
"""

import asyncio
import sys
import traceback
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_tool_execution():
    """Test the actual tool execution in isolation"""
    
    print("=" * 80)
    print("SENIOR ENGINEER DEBUG: Agent Tool Execution")
    print("=" * 80)
    
    try:
        # Add project path
        sys.path.append('.')
        
        # Import the tool functions directly
        from agents.detection_agents import get_new_pairs_dexscreener, get_comprehensive_new_tokens
        
        print("1. Testing direct tool function access...")
        
        # Check if it's a CrewAI Tool object
        print(f"get_new_pairs_dexscreener type: {type(get_new_pairs_dexscreener)}")
        print(f"get_comprehensive_new_tokens type: {type(get_comprehensive_new_tokens)}")
        
        # Try to access the actual function
        if hasattr(get_new_pairs_dexscreener, 'func'):
            actual_func = get_new_pairs_dexscreener.func
            print("Found .func attribute - this is a CrewAI Tool wrapper")
        else:
            print("No .func attribute found")
            
        # Try to get the function via other means
        if hasattr(get_new_pairs_dexscreener, '_func'):
            actual_func = get_new_pairs_dexscreener._func
            print("Found ._func attribute")
        elif hasattr(get_new_pairs_dexscreener, 'function'):
            actual_func = get_new_pairs_dexscreener.function
            print("Found .function attribute")
        else:
            print("No direct function access found")
            
        # Let's try calling the tool through CrewAI's execution method
        print("\n2. Testing CrewAI tool execution...")
        
        try:
            from crewai_tools import tool
            
            # Create a simple test function that doesn't use asyncio.run
            @tool
            def test_api_simple(chain: str) -> str:
                """Simple API test without asyncio conflicts"""
                import requests
                import time
                
                try:
                    start_time = time.time()
                    response = requests.get(
                        f"https://api.dexscreener.com/latest/dex/search?q=solana",
                        timeout=10
                    )
                    duration = time.time() - start_time
                    
                    if response.status_code == 200:
                        data = response.json()
                        pairs = data.get('pairs', [])
                        return f"SUCCESS: Found {len(pairs)} pairs in {duration:.3f}s"
                    else:
                        return f"API_ERROR: Status {response.status_code}"
                        
                except Exception as e:
                    return f"ERROR: {str(e)}"
            
            # Test the simple function
            print("Testing simple tool function...")
            result = test_api_simple.run(chain="solana")
            print(f"Simple tool result: {result}")
            
        except Exception as e:
            print(f"Tool execution test failed: {str(e)}")
            traceback.print_exc()
            
        print("\n3. Testing async-compatible function...")
        
        # Create an async-compatible version
        def get_tokens_sync(chain: str):
            """Synchronous version of token detection"""
            import requests
            import time
            
            try:
                search_queries = ["meme", "sol", "token"]
                current_time = time.time()
                all_results = []
                
                for query in search_queries:
                    url = f"https://api.dexscreener.com/latest/dex/search?q={query}"
                    
                    try:
                        response = requests.get(url, timeout=10)
                        if response.status_code == 200:
                            data = response.json()
                            pairs = data.get('pairs', [])
                            
                            # Filter for recent pairs
                            for pair in pairs:
                                if pair.get('chainId') == chain:
                                    created_at = pair.get('pairCreatedAt')
                                    if created_at:
                                        try:
                                            created_timestamp = int(created_at) / 1000
                                            age_hours = (current_time - created_timestamp) / 3600
                                            if age_hours < 24:
                                                pair['age_hours'] = age_hours
                                                all_results.append(pair)
                                        except:
                                            pass
                                            
                    except Exception as e:
                        print(f"Query {query} failed: {str(e)}")
                        
                # Remove duplicates
                seen_addresses = set()
                unique_results = []
                for result in all_results:
                    address = result.get('baseToken', {}).get('address', '')
                    if address and address not in seen_addresses:
                        seen_addresses.add(address)
                        unique_results.append(result)
                        
                return unique_results[:10]  # Return top 10
                
            except Exception as e:
                print(f"Sync function error: {str(e)}")
                return []
                
        # Test sync version
        print("Testing synchronous token detection...")
        sync_results = get_tokens_sync("solana")
        print(f"Sync function found {len(sync_results)} tokens")
        
        if sync_results:
            sample = sync_results[0]
            print(f"Sample token: {sample.get('baseToken', {}).get('symbol', 'N/A')}")
            
    except Exception as e:
        print(f"Test failed: {str(e)}")
        traceback.print_exc()
        
    print("\n" + "=" * 80)

async def test_agent_with_fixed_tools():
    """Test agent with corrected tool implementations"""
    
    print("4. Testing Agent with Fixed Tools...")
    
    try:
        from crewai import Agent
        from agents.detection_agents import create_groq_llm
        from crewai_tools import tool
        
        # Create a properly working tool
        @tool
        def get_solana_tokens_working(chain: str = "solana") -> str:
            """Get Solana tokens using working synchronous approach"""
            import requests
            import time
            
            try:
                url = "https://api.dexscreener.com/latest/dex/search?q=solana"
                response = requests.get(url, timeout=15)
                
                if response.status_code == 200:
                    data = response.json()
                    pairs = data.get('pairs', [])
                    
                    # Filter for Solana pairs
                    solana_pairs = [p for p in pairs if p.get('chainId') == 'solana']
                    
                    if solana_pairs:
                        # Get a few recent ones
                        recent_pairs = []
                        current_time = time.time()
                        
                        for pair in solana_pairs[:10]:  # Check first 10
                            symbol = pair.get('baseToken', {}).get('symbol', 'Unknown')
                            address = pair.get('baseToken', {}).get('address', 'No address')
                            
                            recent_pairs.append({
                                'symbol': symbol,
                                'address': address[:8] + '...' if len(address) > 8 else address,
                                'chainId': pair.get('chainId')
                            })
                            
                        return f"Found {len(recent_pairs)} Solana tokens: {recent_pairs[:3]}"
                    else:
                        return "No Solana tokens found in search results"
                else:
                    return f"API error: Status {response.status_code}"
                    
            except Exception as e:
                return f"Tool execution error: {str(e)}"
        
        # Test the tool directly
        print("Testing fixed tool directly...")
        tool_result = get_solana_tokens_working.run()
        print(f"Fixed tool result: {tool_result}")
        
        # Create agent with working tool
        print("Creating agent with working tool...")
        
        llm = create_groq_llm("llama3-70b-8192", "detection")
        
        test_agent = Agent(
            role="Token Detection Tester",
            goal="Test token detection with working tools",
            backstory="A test agent for validating token detection functionality",
            tools=[get_solana_tokens_working],
            llm=llm,
            verbose=True,
            max_iter=2
        )
        
        # Create a simple crew task
        from crewai import Task, Crew
        
        test_task = Task(
            description="Use the get_solana_tokens_working tool to find current Solana tokens",
            expected_output="A list of found Solana tokens with their details",
            agent=test_agent
        )
        
        test_crew = Crew(
            agents=[test_agent],
            tasks=[test_task],
            verbose=True
        )
        
        print("Executing test crew...")
        result = await test_crew.kickoff_async()
        print(f"Crew result: {result}")
        
    except Exception as e:
        print(f"Agent test failed: {str(e)}")
        traceback.print_exc()

async def main():
    """Main test execution"""
    await test_tool_execution()
    await test_agent_with_fixed_tools()

if __name__ == "__main__":
    asyncio.run(main())
