#!/usr/bin/env python3
"""
Fixed Detection Agents - Senior Engineer Solution
Resolving CrewAI 2025 tool execution issues with proper synchronous tools
"""

import requests
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from crewai import Agent, LLM
from crewai.tools import tool
import json
import os

def create_groq_llm(model_name: str, context: str = "detection") -> LLM:
    """Create Groq LLM instance with proper configuration"""
    return LLM(
        model=f"groq/{model_name}",
        api_key=os.getenv("GROQ_API_KEY"),
        temperature=0.3,
        max_tokens=2000
    )

# FIXED TOOLS - Proper synchronous implementations for CrewAI 2025

@tool
def get_new_pairs_dexscreener_fixed(chain: str = "solana") -> str:
    """
    Get new token pairs from DEXScreener API using working search approach.
    This is the FIXED version that resolves the 0-token detection issue.
    
    Args:
        chain: Blockchain to search (default: solana)
    
    Returns:
        JSON string of found pairs or error message
    """
    try:
        current_time = time.time()
        search_queries = ["meme", "sol", "token", "new"]
        all_pairs = []
        
        for query in search_queries:
            try:
                url = f"https://api.dexscreener.com/latest/dex/search?q={query}"
                response = requests.get(url, timeout=15)
                
                if response.status_code == 200:
                    data = response.json()
                    pairs = data.get('pairs', [])
                    
                    # Filter for specified chain and recent pairs
                    for pair in pairs:
                        if pair.get('chainId') == chain:
                            created_at = pair.get('pairCreatedAt')
                            if created_at:
                                try:
                                    created_timestamp = int(created_at) / 1000
                                    age_hours = (current_time - created_timestamp) / 3600
                                    
                                    # Only include pairs created in last 24 hours
                                    if age_hours <= 24:
                                        pair_data = {
                                            'symbol': pair.get('baseToken', {}).get('symbol', 'Unknown'),
                                            'address': pair.get('baseToken', {}).get('address', ''),
                                            'chainId': pair.get('chainId'),
                                            'age_hours': round(age_hours, 2),
                                            'priceUsd': pair.get('priceUsd', '0'),
                                            'volume24h': pair.get('volume', {}).get('h24', 0),
                                            'liquidity': pair.get('liquidity', {}).get('usd', 0),
                                            'pairAddress': pair.get('pairAddress', ''),
                                            'dexId': pair.get('dexId', ''),
                                            'created_at': created_at
                                        }
                                        all_pairs.append(pair_data)
                                except (ValueError, TypeError):
                                    continue
                                    
            except requests.RequestException as e:
                continue  # Skip failed queries
                
        # Remove duplicates by address
        seen_addresses = set()
        unique_pairs = []
        for pair in all_pairs:
            address = pair.get('address', '')
            if address and address not in seen_addresses:
                seen_addresses.add(address)
                unique_pairs.append(pair)
                
        # Sort by creation time (newest first)
        unique_pairs.sort(key=lambda x: int(x.get('created_at', 0)), reverse=True)
        
        # Return top 10 newest pairs
        result_pairs = unique_pairs[:10]
        
        if result_pairs:
            return json.dumps({
                'status': 'success',
                'pairs_found': len(result_pairs),
                'chain': chain,
                'pairs': result_pairs
            })
        else:
            return json.dumps({
                'status': 'no_pairs',
                'message': f'No new pairs found for {chain} in last 24 hours',
                'pairs': []
            })
            
    except Exception as e:
        return json.dumps({
            'status': 'error',
            'message': f'Tool execution error: {str(e)}',
            'pairs': []
        })

@tool
def get_comprehensive_new_tokens_fixed(hours: int = 24) -> str:
    """
    Get comprehensive new token data from multiple sources - FIXED version.
    
    Args:
        hours: Time window for new tokens (default: 24 hours)
    
    Returns:
        JSON string of comprehensive token data
    """
    try:
        current_time = time.time()
        cutoff_time = current_time - (hours * 3600)
        
        # Multi-source data collection
        sources_data = {}
        
        # Source 1: DEXScreener trending search
        try:
            trending_queries = ["trending", "pump", "new", "launch"]
            dex_tokens = []
            
            for query in trending_queries:
                url = f"https://api.dexscreener.com/latest/dex/search?q={query}"
                response = requests.get(url, timeout=15)
                
                if response.status_code == 200:
                    data = response.json()
                    pairs = data.get('pairs', [])
                    
                    for pair in pairs:
                        created_at = pair.get('pairCreatedAt')
                        if created_at:
                            try:
                                created_timestamp = int(created_at) / 1000
                                if created_timestamp > cutoff_time:
                                    token_data = {
                                        'source': 'dexscreener',
                                        'symbol': pair.get('baseToken', {}).get('symbol', ''),
                                        'address': pair.get('baseToken', {}).get('address', ''),
                                        'chain': pair.get('chainId', ''),
                                        'created_timestamp': created_timestamp,
                                        'age_hours': round((current_time - created_timestamp) / 3600, 2),
                                        'price_usd': pair.get('priceUsd', '0'),
                                        'volume_24h': pair.get('volume', {}).get('h24', 0),
                                        'liquidity_usd': pair.get('liquidity', {}).get('usd', 0),
                                        'dex': pair.get('dexId', ''),
                                        'pair_address': pair.get('pairAddress', ''),
                                        'query': query
                                    }
                                    dex_tokens.append(token_data)
                            except (ValueError, TypeError):
                                continue
                                
            sources_data['dexscreener'] = dex_tokens
            
        except Exception as e:
            sources_data['dexscreener_error'] = str(e)
            
        # Combine and deduplicate across sources
        all_tokens = []
        seen_addresses = set()
        
        for source, tokens in sources_data.items():
            if isinstance(tokens, list):
                for token in tokens:
                    address = token.get('address', '')
                    if address and address not in seen_addresses:
                        seen_addresses.add(address)
                        all_tokens.append(token)
                        
        # Sort by creation time (newest first)
        all_tokens.sort(key=lambda x: x.get('created_timestamp', 0), reverse=True)
        
        # Categorize tokens
        categories = {
            'ultra_new': [],    # < 1 hour
            'very_new': [],     # 1-6 hours  
            'new': [],          # 6-24 hours
            'trending': []      # High volume/activity
        }
        
        for token in all_tokens:
            age_hours = token.get('age_hours', 999)
            volume = float(token.get('volume_24h', 0))
            
            if age_hours < 1:
                categories['ultra_new'].append(token)
            elif age_hours < 6:
                categories['very_new'].append(token)
            elif age_hours <= 24:
                categories['new'].append(token)
                
            if volume > 10000:  # $10k+ volume
                categories['trending'].append(token)
                
        return json.dumps({
            'status': 'success',
            'timestamp': current_time,
            'hours_searched': hours,
            'total_tokens_found': len(all_tokens),
            'categories': {
                'ultra_new': len(categories['ultra_new']),
                'very_new': len(categories['very_new']), 
                'new': len(categories['new']),
                'trending': len(categories['trending'])
            },
            'top_tokens': all_tokens[:15],  # Top 15 newest
            'sources_status': {
                source: len(data) if isinstance(data, list) else f"error: {data}"
                for source, data in sources_data.items()
            }
        })
        
    except Exception as e:
        return json.dumps({
            'status': 'error',
            'message': f'Comprehensive detection error: {str(e)}',
            'tokens': []
        })

@tool  
def analyze_token_metrics_fixed(token_data: str) -> str:
    """
    Analyze token metrics for risk assessment - FIXED version.
    
    Args:
        token_data: JSON string of token data to analyze
    
    Returns:
        JSON string of analysis results
    """
    try:
        data = json.loads(token_data) if isinstance(token_data, str) else token_data
        
        analysis = {
            'timestamp': time.time(),
            'risk_level': 'unknown',
            'risk_factors': [],
            'positive_signals': [],
            'metrics_analysis': {},
            'recommendation': 'insufficient_data'
        }
        
        # Extract token info
        if 'pairs' in data:
            tokens = data['pairs']
        elif 'top_tokens' in data:
            tokens = data['top_tokens']
        elif isinstance(data, list):
            tokens = data
        else:
            tokens = [data]
            
        if not tokens:
            analysis['risk_level'] = 'no_data'
            analysis['recommendation'] = 'no_tokens_to_analyze'
            return json.dumps(analysis)
            
        # Analyze first token (primary analysis)
        token = tokens[0] if tokens else {}
        
        # Risk factors analysis
        risk_score = 0
        
        # Age analysis
        age_hours = float(token.get('age_hours', 999))
        if age_hours < 1:
            analysis['positive_signals'].append('Ultra new token (< 1 hour)')
            risk_score -= 1
        elif age_hours > 24:
            analysis['risk_factors'].append('Token older than 24 hours')
            risk_score += 1
            
        # Liquidity analysis
        liquidity = float(token.get('liquidity_usd', token.get('liquidity', 0)))
        if liquidity < 1000:
            analysis['risk_factors'].append('Low liquidity (< $1,000)')
            risk_score += 2
        elif liquidity > 50000:
            analysis['positive_signals'].append('High liquidity (> $50,000)')
            risk_score -= 1
            
        # Volume analysis
        volume_24h = float(token.get('volume_24h', token.get('volume24h', 0)))
        if volume_24h < 1000:
            analysis['risk_factors'].append('Low trading volume')
            risk_score += 1
        elif volume_24h > 10000:
            analysis['positive_signals'].append('Active trading volume')
            risk_score -= 1
            
        # Price analysis
        price_usd = float(token.get('price_usd', token.get('priceUsd', 0)))
        if price_usd == 0:
            analysis['risk_factors'].append('No price data available')
            risk_score += 1
            
        # Risk level determination
        if risk_score <= -2:
            analysis['risk_level'] = 'low'
            analysis['recommendation'] = 'potentially_interesting'
        elif risk_score <= 0:
            analysis['risk_level'] = 'medium'
            analysis['recommendation'] = 'proceed_with_caution'
        else:
            analysis['risk_level'] = 'high'
            analysis['recommendation'] = 'high_risk_avoid'
            
        # Detailed metrics
        analysis['metrics_analysis'] = {
            'symbol': token.get('symbol', 'Unknown'),
            'chain': token.get('chain', token.get('chainId', 'Unknown')),
            'age_hours': age_hours,
            'liquidity_usd': liquidity,
            'volume_24h_usd': volume_24h,
            'price_usd': price_usd,
            'risk_score': risk_score
        }
        
        return json.dumps(analysis)
        
    except Exception as e:
        return json.dumps({
            'status': 'error',
            'message': f'Analysis error: {str(e)}',
            'risk_level': 'error'
        })

@tool
def get_birdeye_data_fixed(token_address: str, chain: str = "solana") -> str:
    """
    Get Birdeye API data for token - FIXED version with error handling.
    
    Args:
        token_address: Token contract address
        chain: Blockchain network
    
    Returns:
        JSON string of Birdeye data or error message
    """
    try:
        api_key = os.getenv("BIRDEYE_API_KEY")
        if not api_key:
            return json.dumps({
                'status': 'error',
                'message': 'BIRDEYE_API_KEY not found in environment'
            })
            
        headers = {
            "X-API-KEY": api_key,
            "accept": "application/json"
        }
        
        # Get token overview
        url = f"https://public-api.birdeye.so/defi/token_overview"
        params = {"address": token_address}
        
        response = requests.get(url, headers=headers, params=params, timeout=15)
        
        if response.status_code == 200:
            try:
                data = response.json()
                
                # Extract key metrics
                token_info = data.get('data', {})
                
                result = {
                    'status': 'success',
                    'token_address': token_address,
                    'chain': chain,
                    'symbol': token_info.get('symbol', 'Unknown'),
                    'name': token_info.get('name', 'Unknown'),
                    'decimals': token_info.get('decimals', 0),
                    'price': token_info.get('price', 0),
                    'market_cap': token_info.get('mc', 0),
                    'volume_24h': token_info.get('v24hUSD', 0),
                    'liquidity': token_info.get('liquidity', 0),
                    'holder_count': token_info.get('holder', 0),
                    'creation_time': token_info.get('createdTime', 0),
                    'raw_response': data
                }
                
                return json.dumps(result)
                
            except json.JSONDecodeError:
                return json.dumps({
                    'status': 'error',
                    'message': 'Invalid JSON response from Birdeye API',
                    'status_code': response.status_code
                })
        else:
            return json.dumps({
                'status': 'error',
                'message': f'Birdeye API error: HTTP {response.status_code}',
                'response_text': response.text[:200]  # First 200 chars
            })
            
    except Exception as e:
        return json.dumps({
            'status': 'error',
            'message': f'Birdeye tool error: {str(e)}'
        })

# FIXED AGENT CREATION FUNCTIONS

def create_alpha_detector_agent_fixed() -> Agent:
    """Create Alpha Detector Agent with FIXED tools"""
    return Agent(
        role="Alpha Token Detector",
        goal="Identify and analyze new high-potential tokens across multiple blockchains using advanced detection algorithms",
        backstory="""You are an elite cryptocurrency Alpha Detector with PhD-level expertise in blockchain analysis 
        and token discovery. You specialize in identifying emerging tokens with exceptional growth potential through 
        sophisticated multi-source detection algorithms. Your analysis combines real-time market data, liquidity metrics, 
        and advanced pattern recognition to uncover alpha opportunities before they become mainstream.""",
        tools=[
            get_new_pairs_dexscreener_fixed,
            get_comprehensive_new_tokens_fixed,
            analyze_token_metrics_fixed
        ],
        llm=create_groq_llm("deepseek-r1-distill-llama-70b", "detection"),
        verbose=True,
        max_iter=3,
        allow_delegation=False
    )

def create_multi_chain_scanner_agent_fixed() -> Agent:
    """Create Multi-Chain Scanner Agent with FIXED tools"""
    return Agent(
        role="Multi-Chain Token Scanner",
        goal="Perform comprehensive token discovery across Solana, Ethereum, and Base networks with advanced filtering",
        backstory="""You are a Multi-Chain Token Scanner with deep expertise in cross-chain analysis and token discovery. 
        You possess advanced knowledge of DEX protocols, liquidity patterns, and token launch mechanisms across multiple 
        blockchain ecosystems. Your sophisticated scanning algorithms identify promising tokens across chains while filtering 
        out low-quality launches and potential risks.""",
        tools=[
            get_new_pairs_dexscreener_fixed,
            get_comprehensive_new_tokens_fixed,
            analyze_token_metrics_fixed,
            get_birdeye_data_fixed
        ],
        llm=create_groq_llm("llama3-70b-8192", "analysis"),
        verbose=True,
        max_iter=3,
        allow_delegation=False
    )

def create_risk_assessment_agent_fixed() -> Agent:
    """Create Risk Assessment Agent with FIXED tools"""
    return Agent(
        role="Advanced Risk Assessment Specialist",
        goal="Perform comprehensive risk analysis of detected tokens using advanced metrics and pattern recognition",
        backstory="""You are an Advanced Risk Assessment Specialist with extensive experience in cryptocurrency 
        risk analysis and due diligence. You possess deep knowledge of token economics, smart contract analysis, 
        liquidity assessment, and market manipulation detection. Your expertise helps identify high-potential tokens 
        while avoiding rugpulls, honeypots, and other high-risk investments.""",
        tools=[
            analyze_token_metrics_fixed,
            get_birdeye_data_fixed,
            get_new_pairs_dexscreener_fixed
        ],
        llm=create_groq_llm("qwen/qwen-32b-preview", "verification"),
        verbose=True,
        max_iter=3,
        allow_delegation=False
    )

def create_trend_analyzer_agent_fixed() -> Agent:
    """Create Trend Analyzer Agent with FIXED tools"""  
    return Agent(
        role="Market Trend Analysis Expert",
        goal="Analyze market trends and identify tokens with exceptional growth momentum and viral potential",
        backstory="""You are a Market Trend Analysis Expert with specialized knowledge in cryptocurrency trend analysis, 
        viral token identification, and momentum trading strategies. You excel at recognizing early indicators of token 
        popularity, social media buzz, and market sentiment shifts that precede major price movements. Your analysis 
        combines quantitative metrics with qualitative trend assessment.""",
        tools=[
            get_comprehensive_new_tokens_fixed,
            analyze_token_metrics_fixed,
            get_birdeye_data_fixed
        ],
        llm=create_groq_llm("deepseek-r1-distill-llama-70b", "analysis"),
        verbose=True,
        max_iter=3,
        allow_delegation=False
    )

# Test function
def test_fixed_tools():
    """Test the fixed tools directly"""
    print("Testing fixed DEXScreener tool...")
    result = get_new_pairs_dexscreener_fixed.run(chain="solana")
    print(f"DEXScreener result: {result[:200]}...")
    
    print("\nTesting comprehensive tokens tool...")
    result2 = get_comprehensive_new_tokens_fixed.run(hours=24)
    print(f"Comprehensive result: {result2[:200]}...")

if __name__ == "__main__":
    test_fixed_tools()
