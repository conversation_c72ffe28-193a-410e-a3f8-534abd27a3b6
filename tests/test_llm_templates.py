"""
Unit tests for LLM prompt templates
"""

import pytest
import json
from unittest.mock import patch

from core.llm.templates import (
    PromptTemplate, ChainOfThoughtTemplate, DetectionTemplate,
    SecurityAnalysisTemplate, LiquidityAnalysisTemplate, SocialSentimentTemplate,
    AlertGenerationTemplate, HallucinationCheckTemplate,
    PromptType, get_template, format_prompt_with_template,
    create_chain_of_thought_template, TEMPLATE_REGISTRY
)


class TestChainOfThoughtTemplate:
    """Test chain-of-thought template functionality"""
    
    @pytest.fixture
    def cot_template(self):
        """Create chain-of-thought template for testing"""
        return ChainOfThoughtTemplate(
            name="test_analysis",
            description="Test analysis template",
            reasoning_steps=[
                "Analyze the input data for {data_type}",
                "Calculate relevant metrics using {calculation_method}",
                "Compare against historical patterns",
                "Draw conclusions based on evidence"
            ],
            analysis_domain="test domain",
            output_schema={
                "score": "float (0-100)",
                "confidence": "float (0.0-1.0)",
                "reasoning": "string"
            }
        )
    
    def test_initialization(self, cot_template):
        """Test template initialization"""
        assert cot_template.name == "test_analysis"
        assert cot_template.prompt_type == PromptType.CHAIN_OF_THOUGHT
        assert cot_template.analysis_domain == "test domain"
        assert len(cot_template.reasoning_steps) == 4
        assert cot_template.system_prompt is not None
        assert "PhD-level" in cot_template.system_prompt
    
    def test_required_variables_extraction(self, cot_template):
        """Test extraction of required variables from reasoning steps"""
        required_vars = cot_template.get_required_variables()
        
        assert "data_type" in required_vars
        assert "calculation_method" in required_vars
        assert len(required_vars) == 2
    
    def test_format_prompt(self, cot_template):
        """Test prompt formatting"""
        formatted = cot_template.format(
            data_type="token data",
            calculation_method="statistical analysis",
            token_address="0x123...",
            liquidity_data={"total": 100000, "locked": 80000}
        )
        
        assert "ANALYSIS TASK: Test analysis template" in formatted
        assert "REASONING FRAMEWORK:" in formatted
        assert "1. Analyze the input data for token data" in formatted
        assert "2. Calculate relevant metrics using statistical analysis" in formatted
        assert "DATA PROVIDED:" in formatted
        assert "TOKEN_ADDRESS: 0x123..." in formatted
        assert "OUTPUT SCHEMA:" in formatted
        assert json.dumps(cot_template.output_schema, indent=2) in formatted
    
    def test_validation_missing_variables(self, cot_template):
        """Test validation with missing required variables"""
        with pytest.raises(ValueError) as exc_info:
            cot_template.format(data_type="token data")  # Missing calculation_method
        
        assert "Missing required variables" in str(exc_info.value)
        assert "calculation_method" in str(exc_info.value)
    
    def test_examples_handling(self, cot_template):
        """Test examples in template formatting"""
        cot_template.add_example(
            "Sample input data",
            "Sample analysis output"
        )
        
        formatted = cot_template.format(
            data_type="test",
            calculation_method="test"
        )
        
        assert "EXAMPLES:" in formatted
        assert "Example 1:" in formatted
        assert "Sample input data" in formatted
        assert "Sample analysis output" in formatted


class TestSecurityAnalysisTemplate:
    """Test security analysis template"""
    
    @pytest.fixture
    def security_template(self):
        """Get security analysis template"""
        return SecurityAnalysisTemplate()
    
    def test_initialization(self, security_template):
        """Test security template initialization"""
        assert security_template.name == "security_analysis"
        assert security_template.analysis_domain == "blockchain security"
        assert len(security_template.reasoning_steps) == 6
        assert "Contract verification status" in security_template.reasoning_steps[0]
        assert "Risk score calculation" in security_template.reasoning_steps[5]
    
    def test_output_schema(self, security_template):
        """Test security analysis output schema"""
        schema = security_template.output_schema
        
        assert "security_score" in schema
        assert "risk_factors" in schema
        assert "honeypot_indicators" in schema
        assert "confidence_score" in schema
    
    def test_format_security_analysis(self, security_template):
        """Test security analysis formatting"""
        formatted = security_template.format(
            token_address="0x123...",
            contract_data={"verified": True, "owner": "0xabc..."},
            liquidity_data={"locked": False, "amount": 50000}
        )
        
        assert "blockchain security analyst" in formatted
        assert "Contract verification status" in formatted
        assert "TOKEN_ADDRESS: 0x123..." in formatted
        assert "security_score" in formatted


class TestLiquidityAnalysisTemplate:
    """Test liquidity analysis template"""
    
    @pytest.fixture
    def liquidity_template(self):
        """Get liquidity analysis template"""
        return LiquidityAnalysisTemplate()
    
    def test_reasoning_steps(self, liquidity_template):
        """Test liquidity analysis reasoning steps"""
        steps = liquidity_template.reasoning_steps
        
        assert "Total liquidity depth" in steps[0]
        assert "Price impact assessment" in steps[2]
        assert "Impermanent loss risk" in steps[4]
        assert "sustainability score" in steps[5]
    
    def test_format_liquidity_analysis(self, liquidity_template):
        """Test liquidity analysis formatting"""
        formatted = liquidity_template.format(
            dex_data=[
                {"name": "Uniswap", "liquidity": 100000},
                {"name": "SushiSwap", "liquidity": 50000}
            ],
            price_data={"current": 1.5, "24h_change": 0.1}
        )
        
        assert "DeFi liquidity" in formatted
        assert "liquidity_score" in formatted
        assert "Uniswap" in formatted


class TestSocialSentimentTemplate:
    """Test social sentiment template"""
    
    @pytest.fixture
    def sentiment_template(self):
        """Get social sentiment template"""
        return SocialSentimentTemplate()
    
    def test_initialization(self, sentiment_template):
        """Test sentiment template initialization"""
        assert sentiment_template.name == "social_sentiment"
        assert sentiment_template.prompt_type == PromptType.ANALYSIS
        assert "sentiment analyst" in sentiment_template.system_prompt
    
    def test_format_sentiment_analysis(self, sentiment_template):
        """Test sentiment analysis formatting"""
        formatted = sentiment_template.format(
            token_symbol="MEME",
            twitter_data=[
                {"text": "Great project!", "sentiment": 0.8},
                {"text": "Looks suspicious", "sentiment": -0.3}
            ],
            reddit_data=[
                {"title": "MEME to the moon", "score": 150}
            ]
        )
        
        assert "TOKEN: MEME" in formatted
        assert "TWITTER_DATA:" in formatted
        assert "REDDIT_DATA:" in formatted
        assert "overall_sentiment" in formatted
        assert "bot_activity_detected" in formatted
    
    def test_required_variables(self, sentiment_template):
        """Test required variables for sentiment analysis"""
        required = sentiment_template.get_required_variables()
        assert "token_symbol" in required


class TestAlertGenerationTemplate:
    """Test alert generation template"""
    
    @pytest.fixture
    def alert_template(self):
        """Get alert generation template"""
        return AlertGenerationTemplate()
    
    def test_format_alert_generation(self, alert_template):
        """Test alert generation formatting"""
        formatted = alert_template.format(
            analysis_results={
                "security_score": 85,
                "liquidity_score": 70,
                "overall_risk": "medium"
            },
            market_conditions={
                "trend": "bullish",
                "volatility": "high"
            },
            user_preferences={
                "risk_tolerance": "medium",
                "position_size": 0.05
            }
        )
        
        assert "trading alert" in formatted
        assert "TOKEN ANALYSIS:" in formatted
        assert "MARKET CONDITIONS:" in formatted
        assert "USER PREFERENCES:" in formatted
        assert "Telegram" in formatted
        assert "Discord" in formatted
        assert "Email" in formatted
        assert "Entry strategy" in formatted
        assert "Risk management" in formatted


class TestHallucinationCheckTemplate:
    """Test hallucination check template"""
    
    @pytest.fixture
    def hallucination_template(self):
        """Get hallucination check template"""
        return HallucinationCheckTemplate()
    
    def test_initialization(self, hallucination_template):
        """Test hallucination template initialization"""
        assert hallucination_template.name == "hallucination_check"
        assert hallucination_template.prompt_type == PromptType.VERIFICATION
        assert "fact-checking specialist" in hallucination_template.system_prompt
    
    def test_format_hallucination_check(self, hallucination_template):
        """Test hallucination check formatting"""
        formatted = hallucination_template.format(
            analysis_text="The token has 100% security score and guaranteed profits.",
            source_data={
                "security_analysis": {"score": 75, "issues": ["unverified_contract"]},
                "price_data": {"volatility": "high", "trend": "uncertain"}
            }
        )
        
        assert "ANALYSIS TO VERIFY:" in formatted
        assert "guaranteed profits" in formatted
        assert "SOURCE DATA:" in formatted
        assert "overall_groundedness_score" in formatted
        assert "ungrounded_claims" in formatted
        assert "APPROVE" in formatted or "REJECT" in formatted
    
    def test_required_variables(self, hallucination_template):
        """Test required variables for hallucination check"""
        required = hallucination_template.get_required_variables()
        assert "analysis_text" in required
        assert "source_data" in required


class TestDetectionTemplate:
    """Test detection template"""
    
    @pytest.fixture
    def detection_template(self):
        """Get detection template"""
        return DetectionTemplate(
            name="token_detection",
            description="Extract token information"
        )
    
    def test_format_detection(self, detection_template):
        """Test detection template formatting"""
        formatted = detection_template.format(
            blockchain_data={
                "new_pools": [
                    {"address": "0x123...", "token0": "USDC", "token1": "MEME"}
                ]
            }
        )
        
        assert "blockchain data" in formatted
        assert "BLOCKCHAIN_DATA:" in formatted
        assert "token_address" in formatted
        assert "confidence_score" in formatted
        assert "JSON array" in formatted


class TestTemplateRegistry:
    """Test template registry functionality"""
    
    def test_get_template(self):
        """Test getting templates from registry"""
        security_template = get_template("security_analysis")
        assert isinstance(security_template, SecurityAnalysisTemplate)
        
        liquidity_template = get_template("liquidity_analysis")
        assert isinstance(liquidity_template, LiquidityAnalysisTemplate)
    
    def test_get_unknown_template(self):
        """Test getting unknown template raises error"""
        with pytest.raises(ValueError) as exc_info:
            get_template("unknown_template")
        
        assert "Unknown template" in str(exc_info.value)
        assert "unknown_template" in str(exc_info.value)
    
    def test_template_registry_contents(self):
        """Test template registry contains expected templates"""
        expected_templates = [
            "security_analysis",
            "liquidity_analysis", 
            "social_sentiment",
            "alert_generation",
            "hallucination_check",
            "detection"
        ]
        
        for template_name in expected_templates:
            assert template_name in TEMPLATE_REGISTRY
            assert isinstance(TEMPLATE_REGISTRY[template_name], PromptTemplate)


class TestTemplateUtilities:
    """Test template utility functions"""
    
    def test_create_custom_cot_template(self):
        """Test creating custom chain-of-thought template"""
        custom_template = create_chain_of_thought_template(
            name="custom_analysis",
            description="Custom analysis template",
            reasoning_steps=[
                "Step 1: Analyze {input_data}",
                "Step 2: Calculate {metric_type}",
                "Step 3: Generate conclusions"
            ],
            analysis_domain="custom domain"
        )
        
        assert custom_template.name == "custom_analysis"
        assert custom_template.analysis_domain == "custom domain"
        assert len(custom_template.reasoning_steps) == 3
        assert custom_template.get_required_variables() == ["input_data", "metric_type"]
    
    def test_format_prompt_with_template(self):
        """Test formatting prompt with template utility"""
        prompt, system_prompt = format_prompt_with_template(
            "social_sentiment",
            token_symbol="TEST",
            twitter_data=[{"text": "test tweet"}]
        )
        
        assert "TOKEN: TEST" in prompt
        assert "twitter_data" in prompt
        assert system_prompt is not None
        assert "sentiment analyst" in system_prompt
    
    def test_format_prompt_missing_variables(self):
        """Test formatting with missing variables"""
        with pytest.raises(ValueError):
            format_prompt_with_template(
                "social_sentiment"
                # Missing required token_symbol
            )


class TestPromptTemplateBase:
    """Test base PromptTemplate functionality"""
    
    def test_add_example(self):
        """Test adding examples to template"""
        template = SocialSentimentTemplate()
        
        template.add_example(
            "Positive tweet about token",
            '{"sentiment": 0.8, "confidence": 0.9}'
        )
        
        assert len(template.examples) == 1
        assert template.examples[0]["input"] == "Positive tweet about token"
        assert template.examples[0]["output"] == '{"sentiment": 0.8, "confidence": 0.9}'
    
    def test_get_system_prompt(self):
        """Test getting system prompt"""
        template = SecurityAnalysisTemplate()
        system_prompt = template.get_system_prompt()
        
        assert system_prompt is not None
        assert "PhD-level" in system_prompt
        assert "blockchain security" in system_prompt


if __name__ == "__main__":
    pytest.main([__file__])