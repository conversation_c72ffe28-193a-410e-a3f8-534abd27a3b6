#!/usr/bin/env python3
"""
Quick Test - Fixed Detection Agents
Testing the corrected synchronous agents and tools
"""

import sys
sys.path.append('.')

def test_fixed_agents():
    print("=" * 60)
    print("TESTING FIXED DETECTION AGENTS")
    print("=" * 60)
    
    try:
        from agents.detection_agents import create_groq_llm
        from crewai import Agent, Task, Crew
        
        # Test tool import
        from agents.detection_agents import get_new_pairs_dexscreener, get_comprehensive_new_tokens
        
        print("✅ Successfully imported tools")
        
        # Test tool execution
        print("\n1. Testing DEXScreener tool...")
        result1 = get_new_pairs_dexscreener.run("solana")
        print(f"Result length: {len(result1)} characters")
        print(f"First 100 chars: {result1[:100]}...")
        
        print("\n2. Testing comprehensive tool...")
        result2 = get_comprehensive_new_tokens.run(24)
        print(f"Result length: {len(result2)} characters")
        print(f"First 100 chars: {result2[:100]}...")
        
        # Test agent creation
        print("\n3. Creating test agent...")
        llm = create_groq_llm("llama3-70b-8192")
        
        test_agent = Agent(
            role="Token Detection Tester",
            goal="Test the fixed token detection tools",
            backstory="A test agent for validating our fixed detection system",
            tools=[get_new_pairs_dexscreener, get_comprehensive_new_tokens],
            llm=llm,
            verbose=True,
            max_iter=2
        )
        
        print("✅ Successfully created agent")
        
        # Test crew execution
        print("\n4. Creating and running test crew...")
        
        test_task = Task(
            description="Use get_new_pairs_dexscreener to find new Solana tokens and report the results",
            expected_output="A summary of found tokens with their key details",
            agent=test_agent
        )
        
        test_crew = Crew(
            agents=[test_agent],
            tasks=[test_task],
            verbose=True
        )
        
        print("Running crew (this may take a moment)...")
        result = test_crew.kickoff()
        
        print(f"\n✅ CREW EXECUTION COMPLETED!")
        print(f"Result type: {type(result)}")
        print(f"Result preview: {str(result)[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_agents()
    if success:
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED - AGENTS ARE FIXED!")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ TESTS FAILED - NEED MORE FIXES")
        print("=" * 60)
