"""
MemeGuard Pro - Configuration Management
Environment configuration with validation and type safety
"""

import os
from typing import List, Optional, Dict, Any
from functools import lru_cache
from pydantic import Field, validator, SecretStr
from pydantic_settings import BaseSettings
import logging

logger = logging.getLogger(__name__)


class DatabaseSettings(BaseSettings):
    """Database configuration settings"""
    
    url: str = Field(
        default="postgresql://user:password@localhost:5432/memeguard",
        env="DATABASE_URL",
        description="PostgreSQL database connection URL"
    )
    pool_size: int = Field(
        default=10,
        env="DATABASE_POOL_SIZE",
        ge=1,
        le=50,
        description="Database connection pool size"
    )
    max_overflow: int = Field(
        default=20,
        env="DATABASE_MAX_OVERFLOW", 
        ge=0,
        le=100,
        description="Maximum database connection overflow"
    )
    timescaledb_enabled: bool = Field(
        default=True,
        env="TIMESCALEDB_ENABLED",
        description="Enable TimescaleDB extension for time-series data"
    )


class RedisSettings(BaseSettings):
    """Redis configuration settings"""
    
    url: str = Field(
        default="redis://localhost:6379",
        env="REDIS_URL",
        description="Redis connection URL"
    )
    password: Optional[SecretStr] = Field(
        default=None,
        env="REDIS_PASSWORD",
        description="Redis password"
    )
    db: int = Field(
        default=0,
        env="REDIS_DB",
        ge=0,
        le=15,
        description="Redis database number"
    )


class APISettings(BaseSettings):
    """External API configuration settings"""
    
    # Required API Keys
    openrouter_api_key: SecretStr = Field(
        ...,
        env="OPENROUTER_API_KEY",
        description="OpenRouter API key for DeepSeek R1 access"
    )
    helius_api_key: SecretStr = Field(
        ...,
        env="API_HELIUS_API_KEY", 
        description="Helius API key for Solana data"
    )
    infura_api_key: SecretStr = Field(
        ...,
        env="API_INFURA_API_KEY",
        description="Infura API key for Ethereum/Base data"
    )
    
    # Optional API Keys
    moralis_api_key: Optional[SecretStr] = Field(
        default=None,
        env="API_MORALIS_API_KEY",
        description="Moralis API key for EVM chain data"
    )
    birdeye_api_key: Optional[SecretStr] = Field(
        default=None,
        env="API_BIRDEYE_API_KEY",
        description="Birdeye API key for Solana analytics"
    )
    apify_api_key: Optional[SecretStr] = Field(
        default=None,
        env="API_APIFY_API_KEY",
        description="Apify API key for social media scraping"
    )
    etherscan_api_key: Optional[SecretStr] = Field(
        default=None,
        env="API_ETHERSCAN_API_KEY",
        description="Etherscan API key for Ethereum data"
    )
    tokensniffer_api_key: Optional[SecretStr] = Field(
        default=None,
        env="API_TOKENSNIFFER_KEY",
        description="TokenSniffer API key for security analysis"
    )
    
    # Rate limiting
    rate_limit: int = Field(
        default=1000,
        env="API_RATE_LIMIT",
        ge=1,
        le=10000,
        description="API rate limit (requests per minute)"
    )


class NotificationSettings(BaseSettings):
    """Notification configuration settings"""
    
    discord_webhook_url: Optional[str] = Field(
        default=None,
        env="DISCORD_WEBHOOK_URL",
        description="Discord webhook URL for alerts"
    )
    
    # Email settings
    smtp_server: str = Field(
        default="smtp.gmail.com",
        env="SMTP_SERVER",
        description="SMTP server hostname"
    )
    smtp_port: int = Field(
        default=587,
        env="SMTP_PORT",
        ge=1,
        le=65535,
        description="SMTP server port"
    )
    smtp_username: Optional[str] = Field(
        default=None,
        env="SMTP_USERNAME",
        description="SMTP username"
    )
    smtp_password: Optional[SecretStr] = Field(
        default=None,
        env="SMTP_PASSWORD",
        description="SMTP password"
    )
    alert_email_recipient: Optional[str] = Field(
        default=None,
        env="ALERT_EMAIL_RECIPIENT",
        description="Email recipient for alerts"
    )
    
    @validator('alert_email_recipient')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('Invalid email address')
        return v


class SystemSettings(BaseSettings):
    """System configuration settings"""
    
    # Detection settings
    detection_interval_seconds: int = Field(
        default=30,
        env="DETECTION_INTERVAL_SECONDS",
        ge=5,
        le=300,
        description="Token detection interval in seconds"
    )
    max_tokens_per_cycle: int = Field(
        default=50,
        env="MAX_TOKENS_PER_CYCLE",
        ge=1,
        le=1000,
        description="Maximum tokens to process per cycle"
    )
    
    # Analysis settings
    analysis_timeout_seconds: int = Field(
        default=300,
        env="ANALYSIS_TIMEOUT_SECONDS",
        ge=30,
        le=600,
        description="Analysis timeout in seconds"
    )
    max_concurrent_analyses: int = Field(
        default=10,
        env="MAX_CONCURRENT_ANALYSES",
        ge=1,
        le=100,
        description="Maximum concurrent analyses"
    )
    
    # Consensus settings
    consensus_timeout_seconds: int = Field(
        default=60,
        env="CONSENSUS_TIMEOUT_SECONDS",
        ge=10,
        le=300,
        description="Consensus timeout in seconds"
    )
    min_consensus_votes: int = Field(
        default=3,
        env="MIN_CONSENSUS_VOTES",
        ge=1,
        le=10,
        description="Minimum consensus votes required"
    )
    
    # Risk management
    max_position_size: float = Field(
        default=0.05,
        env="MAX_POSITION_SIZE",
        ge=0.001,
        le=1.0,
        description="Maximum position size as fraction of portfolio"
    )
    max_total_meme_allocation: float = Field(
        default=0.20,
        env="MAX_TOTAL_MEME_ALLOCATION",
        ge=0.01,
        le=1.0,
        description="Maximum total meme token allocation"
    )
    default_stop_loss: float = Field(
        default=-0.50,
        env="DEFAULT_STOP_LOSS",
        ge=-1.0,
        le=-0.01,
        description="Default stop loss percentage"
    )


class LLMSettings(BaseSettings):
    """LLM configuration settings"""
    
    temperature: float = Field(
        default=0.1,
        env="LLM_TEMPERATURE",
        ge=0.0,
        le=2.0,
        description="LLM temperature for response generation"
    )
    max_tokens: int = Field(
        default=3000,
        env="LLM_MAX_TOKENS",
        ge=100,
        le=8000,
        description="Maximum tokens per LLM response"
    )
    timeout_seconds: int = Field(
        default=120,
        env="LLM_TIMEOUT_SECONDS",
        ge=10,
        le=300,
        description="LLM request timeout in seconds"
    )


class MonitoringSettings(BaseSettings):
    """Monitoring and observability settings"""
    
    sentry_dsn: Optional[str] = Field(
        default=None,
        env="SENTRY_DSN",
        description="Sentry DSN for error tracking"
    )
    prometheus_enabled: bool = Field(
        default=True,
        env="PROMETHEUS_ENABLED",
        description="Enable Prometheus metrics"
    )
    prometheus_port: int = Field(
        default=8000,
        env="PROMETHEUS_PORT",
        ge=1024,
        le=65535,
        description="Prometheus metrics port"
    )


class SecuritySettings(BaseSettings):
    """Security configuration settings"""
    
    api_key_encryption_key: Optional[SecretStr] = Field(
        default=None,
        env="API_KEY_ENCRYPTION_KEY",
        description="32-byte encryption key for API keys"
    )
    jwt_secret_key: Optional[SecretStr] = Field(
        default=None,
        env="JWT_SECRET_KEY",
        description="JWT secret key for authentication"
    )
    jwt_access_token_expire_minutes: int = Field(
        default=1440,
        env="JWT_ACCESS_TOKEN_EXPIRE_MINUTES",
        ge=1,
        le=10080,
        description="JWT access token expiration in minutes"
    )
    cors_origins: List[str] = Field(
        default=["http://localhost:3000"],
        env="CORS_ORIGINS",
        description="CORS allowed origins"
    )


class Settings(BaseSettings):
    """Main application settings"""
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8", 
        "case_sensitive": False,
        "validate_assignment": True,
        "extra": "ignore"
    }
    
    # Environment
    environment: str = Field(
        default="development",
        env="ENVIRONMENT",
        description="Application environment"
    )
    debug: bool = Field(
        default=False,
        env="DEBUG",
        description="Enable debug mode"
    )
    test_mode: bool = Field(
        default=False,
        env="TEST_MODE",
        description="Enable test mode with mock data"
    )
    
    # Logging
    log_level: str = Field(
        default="INFO",
        env="LOG_LEVEL",
        description="Logging level"
    )
    
    # API Settings (inline to avoid initialization issues)
    openrouter_api_key: SecretStr = Field(
        ...,
        env="OPENROUTER_API_KEY",
        description="OpenRouter API key for DeepSeek R1 access"
    )
    groq_api_key: SecretStr = Field(
        ...,
        env="GROQ_API_KEY",
        description="Groq API key for high-speed inference"
    )
    helius_api_key: SecretStr = Field(
        ...,
        env="API_HELIUS_API_KEY", 
        description="Helius API key for Solana data"
    )
    infura_api_key: SecretStr = Field(
        ...,
        env="API_INFURA_API_KEY",
        description="Infura API key for Ethereum/Base data"
    )
    
    # Optional API Keys
    moralis_api_key: Optional[SecretStr] = Field(
        default=None,
        env="API_MORALIS_API_KEY",
        description="Moralis API key for EVM chain data"
    )
    birdeye_api_key: Optional[SecretStr] = Field(
        default=None,
        env="API_BIRDEYE_API_KEY",
        description="Birdeye API key for Solana analytics"
    )
    apify_api_key: Optional[SecretStr] = Field(
        default=None,
        env="API_APIFY_API_KEY",
        description="Apify API key for social media scraping"
    )
    
    # Database settings (inline)
    database_url: str = Field(
        default="postgresql://user:password@localhost:5432/memeguard",
        env="DATABASE_URL",
        description="PostgreSQL database connection URL"
    )
    
    # Redis settings (inline)
    redis_url: str = Field(
        default="redis://localhost:6379",
        env="REDIS_URL",
        description="Redis connection URL"
    )
    
    # Monitoring
    sentry_dsn: Optional[str] = Field(
        default=None,
        env="SENTRY_DSN",
        description="Sentry DSN for error tracking"
    )
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()
    
    @validator('environment')
    def validate_environment(cls, v):
        valid_envs = ['development', 'staging', 'production', 'testing']
        if v.lower() not in valid_envs:
            raise ValueError(f'Environment must be one of: {valid_envs}')
        return v.lower()
    
    def get_database_url(self) -> str:
        """Get database URL with proper formatting"""
        return self.database_url
    
    def get_redis_url(self) -> str:
        """Get Redis URL with proper formatting"""
        return self.redis_url
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment == 'production'
    
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.environment == 'development'
    
    def get_api_key(self, service: str) -> Optional[str]:
        """Get API key for a specific service"""
        key_mapping = {
            'openrouter': self.openrouter_api_key,
            'helius': self.helius_api_key,
            'infura': self.infura_api_key,
            'moralis': self.moralis_api_key,
            'birdeye': self.birdeye_api_key,
            'apify': self.apify_api_key,
        }
        
        key = key_mapping.get(service.lower())
        return key.get_secret_value() if key else None


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached application settings
    
    Returns:
        Settings: Application configuration settings
    """
    try:
        # Load environment variables explicitly
        from dotenv import load_dotenv
        load_dotenv()
        
        import os
        
        # Create settings with explicit environment values
        settings_data = {}
        
        # Required API keys
        if os.getenv('OPENROUTER_API_KEY'):
            settings_data['openrouter_api_key'] = os.getenv('OPENROUTER_API_KEY')
        if os.getenv('GROQ_API_KEY'): 
            settings_data['groq_api_key'] = os.getenv('GROQ_API_KEY')
        if os.getenv('API_HELIUS_API_KEY'):
            settings_data['helius_api_key'] = os.getenv('API_HELIUS_API_KEY')
        if os.getenv('API_INFURA_API_KEY'):
            settings_data['infura_api_key'] = os.getenv('API_INFURA_API_KEY')
            
        # Optional API keys
        if os.getenv('API_MORALIS_API_KEY'):
            settings_data['moralis_api_key'] = os.getenv('API_MORALIS_API_KEY')
        if os.getenv('API_BIRDEYE_API_KEY'):
            settings_data['birdeye_api_key'] = os.getenv('API_BIRDEYE_API_KEY')
        if os.getenv('API_APIFY_API_KEY'):
            settings_data['apify_api_key'] = os.getenv('API_APIFY_API_KEY')
            
        # Other settings
        if os.getenv('ENVIRONMENT'):
            settings_data['environment'] = os.getenv('ENVIRONMENT')
        if os.getenv('DEBUG'):
            settings_data['debug'] = os.getenv('DEBUG').lower() == 'true'
        if os.getenv('TEST_MODE'):
            settings_data['test_mode'] = os.getenv('TEST_MODE').lower() == 'true'
        if os.getenv('LOG_LEVEL'):
            settings_data['log_level'] = os.getenv('LOG_LEVEL')
        if os.getenv('DATABASE_URL'):
            settings_data['database_url'] = os.getenv('DATABASE_URL')
        if os.getenv('REDIS_URL'):
            settings_data['redis_url'] = os.getenv('REDIS_URL')
        if os.getenv('SENTRY_DSN'):
            settings_data['sentry_dsn'] = os.getenv('SENTRY_DSN')
            
        settings = Settings(**settings_data)
        logger.info(f"Configuration loaded successfully for environment: {settings.environment}")
        return settings
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        raise


def validate_required_settings() -> None:
    """
    Validate that all required settings are present
    
    Raises:
        ConfigurationError: If required settings are missing
    """
    from .exceptions import ConfigurationError
    
    try:
        settings = get_settings()
        
        # Check required API keys
        required_keys = ['openrouter', 'helius', 'infura']
        missing_keys = []
        
        for key in required_keys:
            if not settings.get_api_key(key):
                missing_keys.append(key.upper())
        
        if missing_keys:
            raise ConfigurationError(
                f"Missing required API keys: {', '.join(missing_keys)}. "
                f"Please check your .env file."
            )
        
        # Validate database connection
        if not settings.database_url or 'localhost' in settings.database_url:
            if settings.is_production():
                raise ConfigurationError(
                    "Production environment requires a proper database URL"
                )
        
        logger.info("All required configuration settings validated successfully")
        
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        raise