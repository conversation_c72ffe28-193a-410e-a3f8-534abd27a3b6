# MemeGuard Pro v4.0 🚀

**PhD-Level AI Agent System for Crypto Token Analysis & Trading Intelligence**

A sophisticated multi-agent system powered by CrewAI and DeepSeek R1, designed to provide institutional-grade analysis of newly launched cryptocurrency tokens across Solana, Ethereum, and Base chains.

## 🎯 System Overview

MemeGuard Pro v4.0 represents a complete reimagining of autonomous crypto trading intelligence, featuring:

- **20+ Specialized AI Agents** with chain-of-thought reasoning
- **Production APIs** including Helius, Infura, DEXScreener, Birdeye, GoPlus Security
- **Byzantine Fault-Tolerant Consensus** mechanism for decision making  
- **Real-time Multi-Chain Detection** across Solana, Ethereum, Base
- **PhD-Level Analysis** with comprehensive risk assessment
- **Intelligent Alert System** via Discord, Email, SMS
- **Advanced Portfolio Management** with quantitative risk controls

## 🏗️ Architecture

```mermaid
graph TB
    A[Detection Agents] --> B[Analysis Agents]
    B --> C[Verification Agents] 
    C --> D[Execution Agents]
    
    subgraph "Detection Layer"
        A1[Solana DEXScreener]
        A2[Solana Birdeye] 
        A3[Ethereum Detection]
        A4[Base Chain Detection]
    end
    
    subgraph "Analysis Layer"
        B1[Security Analysis]
        B2[Honeypot Detection]
        B3[Liquidity Analysis]
        B4[Distribution Analysis]
        B5[Social Sentiment]
        B6[Whale Activity]
    end
    
    subgraph "Verification Layer"
        C1[Cross Validation]
        C2[Blockchain Verification]
        C3[Consensus Mechanism]
    end
    
    subgraph "Execution Layer"
        D1[Strategy Generation]
        D2[Alert System]
        D3[Portfolio Management]
        D4[System Monitoring]
    end
```

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Redis Server
- PostgreSQL (optional)
- 8GB+ RAM recommended

### Installation

1. **Clone the repository:**
```bash
git clone https://github.com/yourusername/memeguard-pro.git
cd memeguard-pro
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your API keys
```

4. **Start Redis:**
```bash
redis-server
```

5. **Run system:**
```bash
python main.py
```

## 🔧 Configuration

### Required API Keys

| Service | Purpose | Get Key |
|---------|---------|---------|
| OpenRouter | DeepSeek R1 Model Access | [openrouter.ai](https://openrouter.ai/) |
| Helius | Solana RPC & APIs | [helius.dev](https://helius.dev/) |
| Infura | Ethereum & Base Chain | [infura.io](https://infura.io/) |

### Optional Enhancements

| Service | Purpose | Benefit |
|---------|---------|---------|
| Birdeye | Solana Token Analytics | Enhanced Solana analysis |
| Moralis | EVM Chain Data | Better holder analysis |
| Apify | Social Media Scraping | Sentiment analysis |
| GoPlus | Security Analysis | Additional security data |

### Environment Configuration

Create `.env` file based on `.env.example`:

```bash
# Core APIs (Required)
OPENROUTER_API_KEY=your_key_here
API_HELIUS_API_KEY=your_key_here
API_INFURA_API_KEY=your_key_here

# Notifications
DISCORD_WEBHOOK_URL=your_webhook_url
ALERT_EMAIL_RECIPIENT=<EMAIL>

# System Settings
DETECTION_INTERVAL_SECONDS=30
LOG_LEVEL=INFO
```

## 🤖 Agent Specifications

### Detection Agents (Group 1)
- **Agent 1:** Solana DEXScreener Scanner
- **Agent 2:** Solana Birdeye Scanner (Redundancy)
- **Agent 3:** Ethereum Detection Agent
- **Agent 4:** Base Chain Detection Agent

### Analysis Agents (Group 2)
- **Agent 5:** Security Analysis Agent
- **Agent 6:** Honeypot Detection Specialist
- **Agent 7:** Liquidity Analysis Agent
- **Agent 8:** Token Distribution Analyzer
- **Agent 9:** Social Sentiment Agent (RAG)
- **Agent 10:** Whale Activity Monitor

### Verification Agents (Group 3)
- **Agent 11:** Cross-Validation Agent
- **Agent 12:** Blockchain Verification Agent
- **Agent 13:** Consensus Mechanism Coordinator
- **Agent 14:** Independent Validation Agent
- **Agent 15:** System Integrity Monitor

### Execution Agents (Group 4)
- **Agent 16:** Strategy Generation Agent
- **Agent 17:** Alert System Coordinator
- **Agent 18:** Portfolio Management Agent
- **Agent 19:** System Monitoring Agent
- **Agent 20:** Performance Analytics Agent

## 🔍 Detection Criteria

### Solana Tokens
- **Age:** < 5 minutes from creation
- **Liquidity:** > $5,000 USD
- **Holders:** > 10 unique addresses
- **Source:** DEXScreener + Birdeye redundancy

### Ethereum Tokens
- **Age:** < 10 minutes (longer confirmation times)
- **Liquidity:** > $10,000 USD (higher gas costs)
- **Holders:** > 20 unique addresses
- **Gas Cost:** Economic viability assessment

### Base Tokens
- **Age:** < 5 minutes (fast L2 blocks)
- **Liquidity:** > $5,000 USD (low gas costs)
- **Holders:** > 15 unique addresses
- **Institutional Potential:** Coinbase integration signals

## 🛡️ Security Analysis

### Multi-Layer Security Assessment
1. **Smart Contract Audit:** Code verification and vulnerability detection
2. **Honeypot Detection:** Multi-methodology trap identification
3. **Tokenomics Analysis:** Supply mechanics and fee structures
4. **Ownership Risk:** Centralization and admin privilege assessment
5. **Liquidity Security:** Lock status and rug pull indicators

### Risk Scoring Framework
- **0.0-0.2:** Low Risk (Green Light)
- **0.2-0.4:** Moderate Risk (Caution)
- **0.4-0.6:** High Risk (Warning)
- **0.6-0.8:** Very High Risk (Avoid)
- **0.8-1.0:** Extreme Risk (Strong Avoid)

## 📊 Consensus Mechanism

### Byzantine Fault Tolerance
- **Minimum Votes:** 67% of agents (Byzantine threshold)
- **Reputation Weighting:** Historical accuracy tracking
- **Outlier Detection:** Statistical anomaly filtering
- **Confidence Calibration:** System-wide uncertainty quantification

### Decision Categories
- **STRONG_BUY:** >80% agreement, high confidence
- **CAUTIOUS_BUY:** 60-80% agreement, moderate confidence
- **MONITOR:** <60% agreement or low confidence
- **AVOID:** 60-80% risk agreement
- **STRONG_AVOID:** >80% high-risk agreement

## 🎯 Trading Strategy Generation

### Portfolio Optimization
- **Max Single Position:** 5% of portfolio
- **Max Meme Allocation:** 20% of total portfolio
- **Position Sizing:** Modified Kelly Criterion
- **Risk Management:** Stop-loss, take-profit, time limits

### Strategy Types
- **Aggressive Buy:** High-confidence, larger position
- **Cautious Buy:** Moderate-confidence, smaller position
- **Monitor:** Track without position
- **Avoid:** Skip investment
- **Strong Avoid:** Alert + blacklist

## 📢 Alert System

### Multi-Channel Delivery
- **Discord:** Real-time notifications with rich embeds
- **Email:** Detailed analysis reports
- **SMS:** Critical alerts only (via Twilio)

### Priority Levels
- **CRITICAL:** Immediate action required (honeypot, system failure)
- **HIGH:** Significant opportunity/risk (high-confidence signals)
- **MEDIUM:** Notable events (moderate-confidence signals)
- **LOW:** Informational updates (system status)

### Anti-Spam Protection
- **Rate Limiting:** Max 5 HIGH priority alerts/hour
- **Duplicate Suppression:** 1-hour cooldown per token
- **Confidence Filtering:** User-configurable thresholds
- **Quiet Hours:** Timezone-aware delivery

## 📈 Performance Metrics

### Key Performance Indicators
- **Detection Rate:** Tokens found per hour
- **Analysis Accuracy:** Verified predictions vs outcomes
- **Consensus Quality:** Agreement level across agents
- **Alert Effectiveness:** User action rate on alerts
- **System Uptime:** Availability and reliability

### Analytics Dashboard
- Real-time system health monitoring
- Agent performance tracking
- Portfolio performance analysis
- Risk-adjusted returns calculation
- Market opportunity identification

## 🔧 System Commands

### Basic Operations
```bash
# Start system
python main.py

# Health check
python main.py --health-check

# System information
python main.py --info

# Custom Redis URL
python main.py --redis redis://remote-host:6379

# Production environment
python main.py --env .env.production
```

### Monitoring Commands
```bash
# View system logs
tail -f logs/memeguard.log

# Check Redis queues
redis-cli llen token_analysis_queue

# Monitor system metrics
curl http://localhost:8000/metrics
```

## 🐳 Docker Deployment

### Using Docker Compose

1. **Create docker-compose.yml:**
```yaml
version: '3.8'
services:
  memeguard:
    build: .
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    volumes:
      - ./.env:/app/.env
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

2. **Deploy:**
```bash
docker-compose up -d
```

## 🔍 Troubleshooting

### Common Issues

**Issue:** "Redis connection failed"
**Solution:** Ensure Redis server is running: `redis-server`

**Issue:** "OpenRouter API key invalid"
**Solution:** Verify API key at https://openrouter.ai/

**Issue:** "High memory usage"
**Solution:** Reduce `MAX_CONCURRENT_ANALYSES` in .env

**Issue:** "Queue bottleneck detected"
**Solution:** Scale up analysis agents or increase timeout values

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
python main.py
```

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

### Development Setup
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
pytest tests/

# Format code
black agents/
isort agents/

# Type checking
mypy agents/
```

## 📄 License

This project is licensed under the MIT License - see [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

MemeGuard Pro is for educational and research purposes. Cryptocurrency trading involves significant risk. Always:

- Conduct your own research
- Never invest more than you can afford to lose
- Consider consulting with financial advisors
- Understand that past performance doesn't guarantee future results

## 🙏 Acknowledgments

- **CrewAI Team** - Multi-agent framework
- **DeepSeek Team** - Advanced reasoning model
- **OpenRouter** - Model access platform
- **Helius, Infura, DEXScreener, Birdeye** - Production APIs
- **Open Source Community** - Various dependencies and tools

## 📞 Support

- **Documentation:** [docs.memeguard.pro](https://docs.memeguard.pro)
- **Discord:** [discord.gg/memeguard](https://discord.gg/memeguard)
- **Email:** <EMAIL>
- **Issues:** [GitHub Issues](https://github.com/yourusername/memeguard-pro/issues)

---

<div align="center">

**MemeGuard Pro v4.0** - *Autonomous Intelligence for Crypto Alpha*

Made with ❤️ by the MemeGuard Team

[Website](https://memeguard.pro) • [Documentation](https://docs.memeguard.pro) • [Discord](https://discord.gg/memeguard) • [Twitter](https://twitter.com/memeguard_pro)

</div>
