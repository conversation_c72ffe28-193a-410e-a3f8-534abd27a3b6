"""
MemeGuard Pro - LLM Models and Data Structures
Core data models for LLM interactions
"""

import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import uuid

from ..exceptions import ValidationError


class ModelProvider(Enum):
    """LLM model providers"""
    OPENROUTER = "openrouter"
    GROQ = "groq"
    ANTHROPIC = "anthropic"
    OPENAI = "openai"


class ModelType(Enum):
    """Types of LLM models based on their capabilities"""
    REASONING = "reasoning"  # For complex analysis and reasoning
    FAST = "fast"  # For quick responses
    CREATIVE = "creative"  # For creative tasks
    CODING = "coding"  # For code generation
    EMBEDDING = "embedding"  # For embeddings


@dataclass
class ModelConfig:
    """Configuration for an LLM model"""
    
    name: str
    provider: ModelProvider
    model_type: ModelType
    model_id: str  # The actual model identifier for API calls
    
    # Performance characteristics
    max_tokens: int = 4096
    context_length: int = 8192
    timeout_seconds: float = 30.0
    
    # Cost information (per token)
    cost_per_token: float = 0.0  # 0 for free models
    cost_per_1k_tokens: float = 0.0
    
    # Routing priority (1-10, lower = higher priority)
    priority: int = 5
    
    # Capabilities
    supports_streaming: bool = True
    supports_function_calling: bool = False
    supports_vision: bool = False
    
    def __post_init__(self):
        """Validate configuration"""
        if self.cost_per_1k_tokens > 0 and self.cost_per_token == 0:
            self.cost_per_token = self.cost_per_1k_tokens / 1000


# Model configurations
DEEPSEEK_R1_CONFIG = ModelConfig(
    name="deepseek-r1",
    provider=ModelProvider.OPENROUTER,
    model_type=ModelType.REASONING,
    model_id="deepseek/deepseek-r1",
    max_tokens=8192,
    context_length=32768,
    timeout_seconds=60.0,
    cost_per_1k_tokens=0.55,  # Input cost
    priority=1,  # High priority for reasoning
    supports_function_calling=True
)

LLAMA_3_3_70B_CONFIG = ModelConfig(
    name="llama-3.3-70b",
    provider=ModelProvider.GROQ,
    model_type=ModelType.FAST,
    model_id="llama-3.3-70b-versatile",
    max_tokens=8192,
    context_length=32768,
    timeout_seconds=15.0,
    cost_per_token=0.0,  # Free on Groq
    priority=2,  # High priority for free model
    supports_function_calling=True
)

LLAMA_3_1_8B_CONFIG = ModelConfig(
    name="llama-3.1-8b",
    provider=ModelProvider.GROQ,
    model_type=ModelType.FAST,
    model_id="llama-3.1-8b-instant",
    max_tokens=8192,
    context_length=32768,
    timeout_seconds=10.0,
    cost_per_token=0.0,  # Free on Groq
    priority=3,  # Good fallback
    supports_function_calling=False
)

CLAUDE_3_5_SONNET_CONFIG = ModelConfig(
    name="claude-3.5-sonnet",
    provider=ModelProvider.OPENROUTER,
    model_type=ModelType.REASONING,
    model_id="anthropic/claude-3.5-sonnet",
    max_tokens=8192,
    context_length=200000,
    timeout_seconds=45.0,
    cost_per_1k_tokens=3.0,  # Input cost
    priority=4,  # High quality but expensive
    supports_function_calling=True,
    supports_vision=True
)

GPT_4O_MINI_CONFIG = ModelConfig(
    name="gpt-4o-mini",
    provider=ModelProvider.OPENROUTER,
    model_type=ModelType.FAST,
    model_id="openai/gpt-4o-mini",
    max_tokens=4096,
    context_length=128000,
    timeout_seconds=20.0,
    cost_per_1k_tokens=0.15,  # Input cost
    priority=5,  # Good balance
    supports_function_calling=True,
    supports_vision=True
)

# Registry of all available models
MODEL_REGISTRY: Dict[str, ModelConfig] = {
    "deepseek-r1": DEEPSEEK_R1_CONFIG,
    "llama-3.3-70b": LLAMA_3_3_70B_CONFIG,
    "llama-3.1-8b": LLAMA_3_1_8B_CONFIG,
    "claude-3.5-sonnet": CLAUDE_3_5_SONNET_CONFIG,
    "gpt-4o-mini": GPT_4O_MINI_CONFIG,
}

# Models by type
MODELS_BY_TYPE: Dict[ModelType, List[ModelConfig]] = {
    ModelType.REASONING: [DEEPSEEK_R1_CONFIG, CLAUDE_3_5_SONNET_CONFIG],
    ModelType.FAST: [LLAMA_3_3_70B_CONFIG, LLAMA_3_1_8B_CONFIG, GPT_4O_MINI_CONFIG],
    ModelType.CREATIVE: [CLAUDE_3_5_SONNET_CONFIG, GPT_4O_MINI_CONFIG],
    ModelType.CODING: [DEEPSEEK_R1_CONFIG, CLAUDE_3_5_SONNET_CONFIG],
    ModelType.EMBEDDING: [],  # Add embedding models as needed
}


@dataclass
class LLMRequest:
    """Request to an LLM model"""
    
    messages: List[Dict[str, str]]
    model_config: ModelConfig
    
    # Optional parameters
    max_tokens: Optional[int] = None
    temperature: float = 0.7
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    
    # System parameters
    correlation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.utcnow)
    
    # Function calling
    functions: Optional[List[Dict[str, Any]]] = None
    function_call: Optional[Union[str, Dict[str, str]]] = None
    
    # Streaming
    stream: bool = False
    
    def __post_init__(self):
        """Validate request"""
        if not self.messages:
            raise ValidationError("Messages cannot be empty", field_name="messages")
        
        if self.max_tokens is None:
            self.max_tokens = self.model_config.max_tokens
        
        # Validate message format
        for i, message in enumerate(self.messages):
            if not isinstance(message, dict):
                raise ValidationError(
                    f"Message {i} must be a dictionary",
                    field_name=f"messages[{i}]"
                )
            
            if "role" not in message or "content" not in message:
                raise ValidationError(
                    f"Message {i} must have 'role' and 'content' fields",
                    field_name=f"messages[{i}]"
                )
    
    def estimate_tokens(self) -> int:
        """Rough estimate of token count"""
        # Simple estimation: ~4 characters per token
        total_chars = sum(len(msg.get("content", "")) for msg in self.messages)
        return max(1, total_chars // 4)
    
    def estimate_cost(self) -> float:
        """Estimate cost for this request"""
        if self.model_config.cost_per_token == 0:
            return 0.0
        
        estimated_tokens = self.estimate_tokens()
        # Assume output tokens are roughly same as input
        total_tokens = estimated_tokens * 2
        return total_tokens * self.model_config.cost_per_token
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API calls"""
        data = {
            "messages": self.messages,
            "model": self.model_config.model_id,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "frequency_penalty": self.frequency_penalty,
            "presence_penalty": self.presence_penalty,
            "stream": self.stream,
        }
        
        # Add function calling if supported
        if self.functions and self.model_config.supports_function_calling:
            data["functions"] = self.functions
            if self.function_call:
                data["function_call"] = self.function_call
        
        return data


@dataclass
class LLMResponse:
    """Response from an LLM model"""
    
    content: str
    model_config: ModelConfig
    
    # Token usage
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    
    # Cost information
    cost_usd: float = 0.0
    
    # Performance metrics
    response_time_seconds: float = 0.0
    
    # Metadata
    correlation_id: str = ""
    timestamp: datetime = field(default_factory=datetime.utcnow)
    
    # Function calling results
    function_call: Optional[Dict[str, Any]] = None
    
    # Raw response for debugging
    raw_response: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Calculate derived fields"""
        if self.total_tokens == 0:
            self.total_tokens = self.prompt_tokens + self.completion_tokens
        
        if self.cost_usd == 0.0 and self.model_config.cost_per_token > 0:
            self.cost_usd = self.total_tokens * self.model_config.cost_per_token


@dataclass
class UsageStats:
    """Usage statistics for tracking"""
    
    model_name: str
    
    # Request counts
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    
    # Token usage
    total_tokens: int = 0
    prompt_tokens: int = 0
    completion_tokens: int = 0
    
    # Cost tracking
    total_cost_usd: float = 0.0
    
    # Performance
    total_response_time: float = 0.0
    avg_response_time: float = 0.0
    
    # Time tracking
    first_request: Optional[datetime] = None
    last_request: Optional[datetime] = None
    
    # Daily counters (reset daily)
    daily_requests: int = 0
    daily_tokens: int = 0
    daily_cost: float = 0.0
    
    def add_request(self, response: LLMResponse, success: bool = True) -> None:
        """Add a request to statistics"""
        self.total_requests += 1
        self.daily_requests += 1
        
        if success:
            self.successful_requests += 1
            
            # Update token stats
            self.total_tokens += response.total_tokens
            self.prompt_tokens += response.prompt_tokens
            self.completion_tokens += response.completion_tokens
            self.daily_tokens += response.total_tokens
            
            # Update cost
            self.total_cost_usd += response.cost_usd
            self.daily_cost += response.cost_usd
            
            # Update performance
            self.total_response_time += response.response_time_seconds
            self.avg_response_time = self.total_response_time / self.successful_requests
        else:
            self.failed_requests += 1
        
        # Update timestamps
        now = datetime.utcnow()
        if self.first_request is None:
            self.first_request = now
        self.last_request = now
    
    def reset_day(self) -> None:
        """Reset daily counters"""
        self.daily_requests = 0
        self.daily_tokens = 0
        self.daily_cost = 0.0
    
    def get_success_rate(self) -> float:
        """Get success rate as percentage"""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "model_name": self.model_name,
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": self.get_success_rate(),
            "total_tokens": self.total_tokens,
            "prompt_tokens": self.prompt_tokens,
            "completion_tokens": self.completion_tokens,
            "total_cost_usd": self.total_cost_usd,
            "avg_response_time": self.avg_response_time,
            "daily_requests": self.daily_requests,
            "daily_tokens": self.daily_tokens,
            "daily_cost": self.daily_cost,
            "first_request": self.first_request.isoformat() if self.first_request else None,
            "last_request": self.last_request.isoformat() if self.last_request else None,
        }


# Utility functions
def get_model_config(model_name: str) -> ModelConfig:
    """Get model configuration by name"""
    if model_name not in MODEL_REGISTRY:
        raise ValidationError(
            f"Unknown model: {model_name}",
            field_name="model_name",
            details={"available_models": list(MODEL_REGISTRY.keys())}
        )
    return MODEL_REGISTRY[model_name]


def get_models_by_type(model_type: ModelType) -> List[ModelConfig]:
    """Get all models of a specific type"""
    return MODELS_BY_TYPE.get(model_type, []).copy()


def get_models_by_provider(provider: ModelProvider) -> List[ModelConfig]:
    """Get all models from a specific provider"""
    return [
        config for config in MODEL_REGISTRY.values()
        if config.provider == provider
    ]


def get_free_models() -> List[ModelConfig]:
    """Get all free models"""
    return [
        config for config in MODEL_REGISTRY.values()
        if config.cost_per_token == 0
    ]


def get_models_with_function_calling() -> List[ModelConfig]:
    """Get models that support function calling"""
    return [
        config for config in MODEL_REGISTRY.values()
        if config.supports_function_calling
    ]


def create_simple_request(
    prompt: str,
    model_name: str = "llama-3.3-70b",
    system_message: Optional[str] = None,
    **kwargs
) -> LLMRequest:
    """Create a simple LLM request"""
    messages = []
    
    if system_message:
        messages.append({"role": "system", "content": system_message})
    
    messages.append({"role": "user", "content": prompt})
    
    model_config = get_model_config(model_name)
    
    return LLMRequest(
        messages=messages,
        model_config=model_config,
        **kwargs
    )