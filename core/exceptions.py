"""
MemeGuard Pro - Exception Classes and Error Handling Framework
Comprehensive error handling with structured error information
"""

import traceback
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class MemeGuardError(Exception):
    """
    Base exception class for all MemeGuard Pro errors
    
    Provides structured error information including:
    - Error code for programmatic handling
    - User-friendly message
    - Technical details for debugging
    - Context information
    - Timestamp
    """
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        """
        Initialize MemeGuard error
        
        Args:
            message: Human-readable error message
            error_code: Unique error code for programmatic handling
            details: Technical details about the error
            context: Context information when error occurred
            cause: Original exception that caused this error
        """
        super().__init__(message)
        
        self.message = message
        self.error_code = error_code or self.__class__.__name__.upper()
        self.details = details or {}
        self.context = context or {}
        self.cause = cause
        self.timestamp = datetime.utcnow()
        self.traceback_str = traceback.format_exc()
        
        # Log the error
        self._log_error()
    
    def _log_error(self) -> None:
        """Log the error with structured information"""
        logger.error(
            f"MemeGuard Error: {self.message}",
            extra={
                'error_code': self.error_code,
                'error_class': self.__class__.__name__,
                'details': self.details,
                'context': self.context,
                'timestamp': self.timestamp.isoformat(),
                'cause': str(self.cause) if self.cause else None,
            },
            exc_info=self.cause if self.cause else True
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for serialization"""
        return {
            'error_code': self.error_code,
            'error_class': self.__class__.__name__,
            'message': self.message,
            'details': self.details,
            'context': self.context,
            'timestamp': self.timestamp.isoformat(),
            'cause': str(self.cause) if self.cause else None,
        }
    
    def __str__(self) -> str:
        """String representation of the error"""
        return f"[{self.error_code}] {self.message}"
    
    def __repr__(self) -> str:
        """Detailed representation of the error"""
        return (
            f"{self.__class__.__name__}("
            f"message='{self.message}', "
            f"error_code='{self.error_code}', "
            f"details={self.details}, "
            f"context={self.context})"
        )


class ConfigurationError(MemeGuardError):
    """
    Raised when there are configuration-related errors
    
    Examples:
    - Missing required environment variables
    - Invalid configuration values
    - Configuration validation failures
    """
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if config_key:
            details['config_key'] = config_key
        if config_value is not None:
            details['config_value'] = str(config_value)
        
        super().__init__(
            message=message,
            error_code="CONFIG_ERROR",
            details=details,
            **kwargs
        )


class APIError(MemeGuardError):
    """
    Raised when external API calls fail
    
    Examples:
    - API rate limiting
    - Authentication failures
    - Network timeouts
    - Invalid API responses
    """
    
    def __init__(
        self,
        message: str,
        api_service: Optional[str] = None,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if api_service:
            details['api_service'] = api_service
        if status_code:
            details['status_code'] = status_code
        if response_data:
            details['response_data'] = response_data
        
        # Remove error_code from kwargs to avoid conflict
        kwargs.pop('error_code', None)
        
        super().__init__(
            message=message,
            error_code="API_ERROR",
            details=details,
            **kwargs
        )


class ValidationError(MemeGuardError):
    """
    Raised when data validation fails
    
    Examples:
    - Invalid token addresses
    - Malformed API responses
    - Schema validation failures
    - Data type mismatches
    """
    
    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        validation_rule: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if field_name:
            details['field_name'] = field_name
        if field_value is not None:
            details['field_value'] = str(field_value)
        if validation_rule:
            details['validation_rule'] = validation_rule
        
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=details,
            **kwargs
        )


class ConsensusError(MemeGuardError):
    """
    Raised when agent consensus mechanisms fail
    
    Examples:
    - Insufficient consensus votes
    - Conflicting agent opinions
    - Consensus timeout
    - Byzantine fault detection
    """
    
    def __init__(
        self,
        message: str,
        consensus_type: Optional[str] = None,
        votes_received: Optional[int] = None,
        votes_required: Optional[int] = None,
        conflicting_agents: Optional[List[str]] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if consensus_type:
            details['consensus_type'] = consensus_type
        if votes_received is not None:
            details['votes_received'] = votes_received
        if votes_required is not None:
            details['votes_required'] = votes_required
        if conflicting_agents:
            details['conflicting_agents'] = conflicting_agents
        
        super().__init__(
            message=message,
            error_code="CONSENSUS_ERROR",
            details=details,
            **kwargs
        )


class SecurityError(MemeGuardError):
    """
    Raised when security-related issues are detected
    
    Examples:
    - Honeypot detection
    - Rug pull indicators
    - Suspicious contract behavior
    - Authentication failures
    """
    
    def __init__(
        self,
        message: str,
        security_issue: Optional[str] = None,
        risk_level: Optional[str] = None,
        token_address: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if security_issue:
            details['security_issue'] = security_issue
        if risk_level:
            details['risk_level'] = risk_level
        if token_address:
            details['token_address'] = token_address
        
        super().__init__(
            message=message,
            error_code="SECURITY_ERROR",
            details=details,
            **kwargs
        )


class AgentError(MemeGuardError):
    """
    Raised when agent-specific errors occur
    
    Examples:
    - Agent initialization failures
    - Agent communication errors
    - Agent timeout
    - Agent state corruption
    """
    
    def __init__(
        self,
        message: str,
        agent_id: Optional[str] = None,
        agent_type: Optional[str] = None,
        agent_state: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if agent_id:
            details['agent_id'] = agent_id
        if agent_type:
            details['agent_type'] = agent_type
        if agent_state:
            details['agent_state'] = agent_state
        
        super().__init__(
            message=message,
            error_code="AGENT_ERROR",
            details=details,
            **kwargs
        )


class DatabaseError(MemeGuardError):
    """
    Raised when database operations fail
    
    Examples:
    - Connection failures
    - Query timeouts
    - Data integrity violations
    - Migration failures
    """
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        table_name: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if operation:
            details['operation'] = operation
        if table_name:
            details['table_name'] = table_name
        
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details=details,
            **kwargs
        )


class RateLimitError(APIError):
    """
    Raised when API rate limits are exceeded
    
    Specialized APIError for rate limiting scenarios
    """
    
    def __init__(
        self,
        message: str,
        api_service: str,
        retry_after: Optional[int] = None,
        requests_remaining: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if retry_after:
            details['retry_after'] = retry_after
        if requests_remaining is not None:
            details['requests_remaining'] = requests_remaining
        
        # Remove error_code from kwargs to avoid conflict
        kwargs.pop('error_code', None)
        
        super().__init__(
            message=message,
            api_service=api_service,
            error_code="RATE_LIMIT_ERROR",
            details=details,
            **kwargs
        )


class TimeoutError(MemeGuardError):
    """
    Raised when operations exceed their timeout limits
    
    Examples:
    - API request timeouts
    - Agent processing timeouts
    - Consensus timeouts
    - Database query timeouts
    """
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        timeout_seconds: Optional[float] = None,
        elapsed_seconds: Optional[float] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if operation:
            details['operation'] = operation
        if timeout_seconds:
            details['timeout_seconds'] = timeout_seconds
        if elapsed_seconds:
            details['elapsed_seconds'] = elapsed_seconds
        
        super().__init__(
            message=message,
            error_code="TIMEOUT_ERROR",
            details=details,
            **kwargs
        )


class HallucinationError(MemeGuardError):
    """
    Raised when AI hallucinations are detected
    
    Examples:
    - Ungrounded claims
    - Inconsistent analysis
    - Fabricated data
    - Logic violations
    """
    
    def __init__(
        self,
        message: str,
        agent_id: Optional[str] = None,
        claim: Optional[str] = None,
        groundedness_score: Optional[float] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if agent_id:
            details['agent_id'] = agent_id
        if claim:
            details['claim'] = claim
        if groundedness_score is not None:
            details['groundedness_score'] = groundedness_score
        
        super().__init__(
            message=message,
            error_code="HALLUCINATION_ERROR",
            details=details,
            **kwargs
        )


class AuthenticationError(APIError):
    """
    Raised when authentication fails
    
    Examples:
    - Invalid API keys
    - Expired tokens
    - Insufficient permissions
    - Authentication service failures
    """
    
    def __init__(
        self,
        message: str,
        api_service: Optional[str] = None,
        auth_method: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if auth_method:
            details['auth_method'] = auth_method
        
        # Remove error_code from kwargs to avoid conflict
        kwargs.pop('error_code', None)
        
        super().__init__(
            message=message,
            api_service=api_service,
            error_code="AUTHENTICATION_ERROR",
            details=details,
            **kwargs
        )


class DataProcessingError(MemeGuardError):
    """
    Raised when data processing operations fail
    
    Examples:
    - Data parsing errors
    - Transformation failures
    - Aggregation errors
    - Data quality issues
    """
    
    def __init__(
        self,
        message: str,
        data_source: Optional[str] = None,
        processing_stage: Optional[str] = None,
        data_sample: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if data_source:
            details['data_source'] = data_source
        if processing_stage:
            details['processing_stage'] = processing_stage
        if data_sample:
            details['data_sample'] = data_sample
        
        super().__init__(
            message=message,
            error_code="DATA_PROCESSING_ERROR",
            details=details,
            **kwargs
        )


class NetworkError(APIError):
    """
    Raised when network-related errors occur
    
    Examples:
    - Connection timeouts
    - DNS resolution failures
    - Network unreachable
    - SSL/TLS errors
    """
    
    def __init__(
        self,
        message: str,
        api_service: Optional[str] = None,
        network_issue: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if network_issue:
            details['network_issue'] = network_issue
        
        # Remove error_code from kwargs to avoid conflict
        kwargs.pop('error_code', None)
        
        super().__init__(
            message=message,
            api_service=api_service,
            error_code="NETWORK_ERROR",
            details=details,
            **kwargs
        )


def handle_exception(
    func_name: str,
    exception: Exception,
    context: Optional[Dict[str, Any]] = None,
    reraise: bool = True
) -> Optional[MemeGuardError]:
    """
    Handle and convert exceptions to MemeGuard errors
    
    Args:
        func_name: Name of the function where error occurred
        exception: Original exception
        context: Additional context information
        reraise: Whether to reraise the converted exception
    
    Returns:
        MemeGuardError: Converted exception (if not reraised)
    
    Raises:
        MemeGuardError: Converted exception (if reraise=True)
    """
    context = context or {}
    context['function'] = func_name
    
    # If it's already a MemeGuard error, just add context
    if isinstance(exception, MemeGuardError):
        exception.context.update(context)
        if reraise:
            raise exception
        return exception
    
    # Convert common exceptions to appropriate MemeGuard errors
    if isinstance(exception, (ConnectionError, TimeoutError)):
        error = APIError(
            message=f"Connection error in {func_name}: {str(exception)}",
            context=context,
            cause=exception
        )
    elif isinstance(exception, ValueError):
        error = ValidationError(
            message=f"Validation error in {func_name}: {str(exception)}",
            context=context,
            cause=exception
        )
    elif isinstance(exception, KeyError):
        error = ConfigurationError(
            message=f"Configuration error in {func_name}: {str(exception)}",
            context=context,
            cause=exception
        )
    else:
        # Generic MemeGuard error for unknown exceptions
        error = MemeGuardError(
            message=f"Unexpected error in {func_name}: {str(exception)}",
            context=context,
            cause=exception
        )
    
    if reraise:
        raise error
    return error


def error_handler(func):
    """
    Decorator to automatically handle and convert exceptions
    
    Usage:
        @error_handler
        def my_function():
            # Function implementation
            pass
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            handle_exception(func.__name__, e)
    
    return wrapper


class ErrorCollector:
    """
    Utility class to collect and manage multiple errors
    
    Useful for batch operations where you want to collect
    all errors rather than failing on the first one
    """
    
    def __init__(self):
        self.errors: List[MemeGuardError] = []
    
    def add_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> None:
        """Add an error to the collection"""
        if isinstance(error, MemeGuardError):
            if context:
                error.context.update(context)
            self.errors.append(error)
        else:
            converted_error = handle_exception(
                "ErrorCollector.add_error",
                error,
                context,
                reraise=False
            )
            self.errors.append(converted_error)
    
    def has_errors(self) -> bool:
        """Check if there are any errors"""
        return len(self.errors) > 0
    
    def get_error_count(self) -> int:
        """Get the number of errors"""
        return len(self.errors)
    
    def get_errors_by_type(self, error_type: type) -> List[MemeGuardError]:
        """Get errors of a specific type"""
        return [error for error in self.errors if isinstance(error, error_type)]
    
    def get_critical_errors(self) -> List[MemeGuardError]:
        """Get critical errors (SecurityError, ConsensusError, etc.)"""
        critical_types = (SecurityError, ConsensusError, HallucinationError)
        return [error for error in self.errors if isinstance(error, critical_types)]
    
    def raise_if_critical(self) -> None:
        """Raise exception if there are critical errors"""
        critical_errors = self.get_critical_errors()
        if critical_errors:
            raise critical_errors[0]  # Raise the first critical error
    
    def to_summary(self) -> Dict[str, Any]:
        """Get a summary of all errors"""
        error_counts = {}
        for error in self.errors:
            error_type = error.__class__.__name__
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
        
        return {
            'total_errors': len(self.errors),
            'error_counts': error_counts,
            'critical_errors': len(self.get_critical_errors()),
            'errors': [error.to_dict() for error in self.errors]
        }
    
    def clear(self) -> None:
        """Clear all errors"""
        self.errors.clear()