"""
Agent Coordinator - Central orchestration of all agent groups
Production-ready multi-agent system coordination with fault tolerance
"""

import asyncio
import time
import os
import json
import logging
import platform
import psutil
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import redis.asyncio as redis
from dataclasses import dataclass

# Import all agent group coordinators
from .detection_agents import DetectionCrewCoordinator
from .analysis_agents import AnalysisCrewCoordinator  
from .verification_agents import VerificationCrewCoordinator
from .execution_agents import ExecutionCrewCoordinator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def print_system_banner():
    """Print MemeGuard Pro system banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════╗
    ║                    MemeGuard Pro v1.0                    ║
    ║              Advanced Meme Coin Intelligence             ║
    ║                 Multi-Agent Detection System            ║
    ╠══════════════════════════════════════════════════════════╣
    ║  🚀 20+ Specialized AI Agents                           ║
    ║  🔍 Real-time Multi-chain Detection                     ║
    ║  🧠 PhD-level Analysis & Research                       ║
    ║  🛡️  Byzantine Fault Tolerance                          ║
    ║  ⚡ Sub-60 Second Processing Pipeline                    ║
    ╚══════════════════════════════════════════════════════════╝
    """
    print(banner)

def get_system_info():
    """Get comprehensive system information"""
    import platform
    import psutil
    from datetime import datetime
    
    try:
        return {
            "name": "MemeGuard Pro",
            "version": "v4.0",
            "description": "PhD-level AI Agent Trading System",
            "agent_count": "20+",
            "technology_stack": [
                "CrewAI 2025", "DeepSeek R1", "LangChain", "FastAPI", 
                "PostgreSQL", "TimescaleDB", "Redis", "ChromaDB",
                "Web3", "Solana", "Multi-chain Support"
            ],
            "features": [
                "🚀 20+ Specialized AI Agents",
                "🔍 Real-time Multi-chain Detection (Solana, Ethereum, Base)",
                "🧠 PhD-level Analysis & Research Framework",
                "🛡️  Byzantine Fault Tolerance Consensus",
                "⚡ Sub-60 Second Processing Pipeline",
                "📊 Advanced Technical Analysis",
                "🔒 Security & Honeypot Detection",
                "💰 Liquidity & Distribution Analysis",
                "📱 Multi-channel Alert System (Discord, Email, SMS)",
                "🎯 Real-time Performance Analytics",
                "🌐 Social Sentiment & Whale Tracking",
                "⚡ High-frequency Data Processing"
            ],
            "runtime_info": {
                "timestamp": datetime.now().isoformat(),
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "cpu_count": psutil.cpu_count(),
                "memory_gb": round(psutil.virtual_memory().total / (1024**3), 2),
                "cpu_usage": psutil.cpu_percent(interval=1),
                "memory_usage": psutil.virtual_memory().percent,
                "debug_mode": os.getenv("DEBUG", "false").lower() == "true",
                "test_mode": os.getenv("TEST_MODE", "false").lower() == "true",
                "log_level": os.getenv("LOG_LEVEL", "INFO"),
                "detection_interval": int(os.getenv("DETECTION_INTERVAL_SECONDS", "30"))
            },
            "apis_status": {
                "openrouter": "✅" if os.getenv("API_OPENROUTER_API_KEY") else "❌",
                "helius": "✅" if os.getenv("API_HELIUS_API_KEY") else "❌",
                "infura": "✅" if os.getenv("API_INFURA_API_KEY") else "❌",
                "birdeye": "✅" if os.getenv("API_BIRDEYE_API_KEY") else "❌",
                "apify": "✅" if os.getenv("API_APIFY_API_KEY") else "❌"
            }
        }
    except Exception as e:
        logger.warning(f"Error getting system info: {e}")
        return {"error": str(e)}

@dataclass
class SystemMetrics:
    """System-wide performance metrics"""
    total_tokens_processed: int
    detection_rate: float
    analysis_success_rate: float
    verification_consensus_rate: float
    execution_success_rate: float
    average_processing_time: float
    system_uptime: float
    error_rate: float

class AgentSystemManager:
    """
    Production-ready agent system manager with comprehensive monitoring,
    fault tolerance, and performance optimization.
    """
    
    def __init__(self, redis_url: str = "redis://localhost:6379", api_manager=None, chroma_client=None):
        self.redis_url = redis_url
        self.api_manager = api_manager
        self.chroma_client = chroma_client
        
        # System state tracking
        self.system_start_time = datetime.now(timezone.utc)
        self.is_running = False
        self.health_check_interval = 60  # seconds
        self.performance_metrics = SystemMetrics(
            total_tokens_processed=0,
            detection_rate=0.0,
            analysis_success_rate=0.0,
            verification_consensus_rate=0.0,
            execution_success_rate=0.0,
            average_processing_time=0.0,
            system_uptime=0.0,
            error_rate=0.0
        )
        
        # Initialize Redis connection
        self.redis_client = None
        
        # Agent coordinators (initialized later)
        self.coordinators = {}
        
    async def initialize(self):
        """Initialize all system components"""
        
        logger.info("Initializing MemeGuard Pro Agent System...")
        
        try:
            # Initialize Redis connection
            self.redis_client = redis.from_url(self.redis_url)
            await self.redis_client.ping()
            logger.info("Redis connection established")
            
            # Initialize API manager if not provided
            if not self.api_manager:
                self.api_manager = APIManager()
                await self.api_manager.initialize()
            
            # Initialize agent coordinators - Just detection for testing
            logger.info("Initializing detection coordinator...")
            try:
                detection_coordinator = DetectionCrewCoordinator(self.redis_client, self.api_manager)
                self.coordinators = {
                    'detection': detection_coordinator,
                }
                logger.info(f"Detection coordinator initialized successfully. Available coordinators: {list(self.coordinators.keys())}")
            except Exception as e:
                logger.error(f"Failed to initialize detection coordinator: {str(e)}")
                raise
                
            # Skip other coordinators for testing
            # 'analysis': AnalysisCrewCoordinator(self.redis_client, self.api_manager, self.chroma_client),
            # 'verification': VerificationCrewCoordinator(self.redis_client, self.api_manager),
            # 'execution': ExecutionCrewCoordinator(self.redis_client, self.api_manager)
            
            logger.info("All agent coordinators initialized successfully")
            
            # Clear any stale queues
            await self._clear_stale_queues()
            
            # Initialize system monitoring
            await self._initialize_monitoring()
            
            logger.info("MemeGuard Pro Agent System initialized successfully")
            
        except Exception as e:
            logger.error(f"System initialization failed: {str(e)}")
            raise
    
    async def start(self):
        """Start the complete agent system"""
        
        if self.is_running:
            logger.warning("System is already running")
            return
        
        logger.info("Starting MemeGuard Pro Agent System...")
        self.is_running = True
        
        try:
            # Start all agent coordinators in parallel
            tasks = [
                # Detection cycle (continuous)
                asyncio.create_task(self._run_detection_cycle()),
                
                # Skip other queues for detection testing
                # asyncio.create_task(self.coordinators['analysis'].process_analysis_queue()),
                # asyncio.create_task(self.coordinators['verification'].process_verification_queue()),
                # asyncio.create_task(self.coordinators['execution'].process_execution_queue()),
                
                # System monitoring
                asyncio.create_task(self._run_system_monitoring()),
                
                # Health checks
                asyncio.create_task(self._run_health_checks()),
                
                # Performance metrics collection
                asyncio.create_task(self._collect_performance_metrics())
            ]
            
            logger.info("All agent coordinators started successfully")
            
            # Run all tasks
            await asyncio.gather(*tasks)
            
        except KeyboardInterrupt:
            logger.info("Received shutdown signal")
            await self.shutdown()
        except Exception as e:
            logger.error(f"System runtime error: {str(e)}")
            await self.shutdown()
            raise
    
    async def shutdown(self):
        """Graceful system shutdown"""
        
        logger.info("Shutting down MemeGuard Pro Agent System...")
        self.is_running = False
        
        try:
            # Close Redis connection
            if self.redis_client:
                await self.redis_client.close()
                logger.info("Redis connection closed")
            
            # Shutdown API manager
            if self.api_manager:
                await self.api_manager.shutdown()
                logger.info("API manager shutdown complete")
            
            logger.info("MemeGuard Pro Agent System shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {str(e)}")
    
    async def _run_detection_cycle(self):
        """Run continuous detection cycles with configurable intervals"""
        
        detection_interval = int(os.getenv('DETECTION_INTERVAL_SECONDS', '30'))
        
        while self.is_running:
            try:
                start_time = time.time()
                
                # Debug logging
                logger.info(f"Running detection cycle. Available coordinators: {list(self.coordinators.keys())}")
                
                # Run detection cycle
                detected_tokens = await self.coordinators['detection'].run_detection_cycle()
                
                processing_time = time.time() - start_time
                
                logger.info(f"Detection cycle completed: {len(detected_tokens)} tokens detected in {processing_time:.2f}s")
                
                # Update metrics
                await self._update_detection_metrics(len(detected_tokens), processing_time)
                
                # Wait before next cycle
                await asyncio.sleep(detection_interval)
                
            except Exception as e:
                logger.error(f"Detection cycle error: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                await asyncio.sleep(10)  # Brief pause on error
    
    async def _run_system_monitoring(self):
        """Continuous system monitoring and alerting"""
        
        while self.is_running:
            try:
                # Monitor queue lengths
                queue_lengths = {
                    "analysis_queue": await self.redis_client.llen("token_analysis_queue"),
                    "verification_queue": await self.redis_client.llen("token_verification_queue"), 
                    "execution_queue": await self.redis_client.llen("token_execution_queue")
                }
                
                # Check for queue bottlenecks
                for queue_name, length in queue_lengths.items():
                    if length > 100:  # Threshold for queue backup
                        logger.warning(f"Queue bottleneck detected: {queue_name} has {length} items")
                        
                        # Send alert for critical bottlenecks
                        if length > 500:
                            await self._send_system_alert(
                                "CRITICAL",
                                f"Critical queue bottleneck: {queue_name}",
                                f"Queue {queue_name} has {length} items - system may be overwhelmed"
                            )
                
                # Monitor system resources
                await self._monitor_system_resources()
                
                await asyncio.sleep(30)  # Monitor every 30 seconds
                
            except Exception as e:
                logger.error(f"System monitoring error: {str(e)}")
                await asyncio.sleep(60)
    
    async def _run_health_checks(self):
        """Periodic system health checks"""
        
        while self.is_running:
            try:
                health_status = await self.get_system_health()
                
                # Log health status
                logger.info(f"System health check: {health_status['overall_status']}")
                
                # Check for critical issues
                if health_status['overall_status'] == 'degraded':
                    logger.warning("System performance degraded")
                elif health_status['overall_status'] == 'critical':
                    logger.error("Critical system issues detected")
                    await self._send_system_alert(
                        "CRITICAL",
                        "System Health Critical",
                        f"Critical issues detected: {health_status['issues']}"
                    )
                
                await asyncio.sleep(self.health_check_interval)
                
            except Exception as e:
                logger.error(f"Health check error: {str(e)}")
                await asyncio.sleep(self.health_check_interval)
    
    async def _collect_performance_metrics(self):
        """Collect and update performance metrics"""
        
        while self.is_running:
            try:
                # Collect metrics from Redis
                total_processed = await self.redis_client.get("total_tokens_processed") or 0
                total_errors = await self.redis_client.get("total_errors") or 0
                
                # Calculate rates
                uptime_hours = (datetime.now(timezone.utc) - self.system_start_time).total_seconds() / 3600
                
                self.performance_metrics = SystemMetrics(
                    total_tokens_processed=int(total_processed),
                    detection_rate=0.0,  # Will be calculated from recent data
                    analysis_success_rate=0.0,
                    verification_consensus_rate=0.0,
                    execution_success_rate=0.0,
                    average_processing_time=0.0,
                    system_uptime=uptime_hours,
                    error_rate=float(total_errors) / max(float(total_processed), 1)
                )
                
                # Store metrics
                await self.redis_client.setex(
                    "system_metrics",
                    3600,  # 1 hour expiry
                    json.dumps({
                        "metrics": self.performance_metrics.__dict__,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    })
                )
                
                await asyncio.sleep(300)  # Update every 5 minutes
                
            except Exception as e:
                logger.error(f"Metrics collection error: {str(e)}")
                await asyncio.sleep(300)
    
    async def _clear_stale_queues(self):
        """Clear any stale queue items on startup"""
        
        queues = [
            "token_analysis_queue",
            "token_verification_queue", 
            "token_execution_queue",
            "system_events",
            "execution_results"
        ]
        
        for queue in queues:
            length = await self.redis_client.llen(queue)
            if length > 0:
                logger.info(f"Clearing {length} stale items from {queue}")
                await self.redis_client.delete(queue)
    
    async def _initialize_monitoring(self):
        """Initialize system monitoring components"""
        
        # Initialize counters
        await self.redis_client.set("total_tokens_processed", 0)
        await self.redis_client.set("total_errors", 0)
        
        # Store system start time
        await self.redis_client.set(
            "system_start_time", 
            self.system_start_time.isoformat()
        )
        
        logger.info("System monitoring initialized")
    
    async def _update_detection_metrics(self, tokens_detected: int, processing_time: float):
        """Update detection cycle metrics"""
        
        await self.redis_client.incr("detection_cycles_completed")
        await self.redis_client.incr("total_tokens_detected", tokens_detected)
        
        # Store recent processing times for average calculation
        await self.redis_client.lpush("recent_processing_times", processing_time)
        await self.redis_client.ltrim("recent_processing_times", 0, 99)  # Keep last 100
    
    async def _monitor_system_resources(self):
        """Monitor system resource usage"""
        
        try:
            import psutil
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # Store metrics
            resource_metrics = {
                "cpu_percent": cpu_percent,
                "memory_percent": memory_percent,
                "disk_percent": disk_percent,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            await self.redis_client.setex(
                "system_resources",
                300,  # 5 minute expiry
                json.dumps(resource_metrics)
            )
            
            # Alert on high resource usage
            if cpu_percent > 90:
                await self._send_system_alert("HIGH", "High CPU Usage", f"CPU usage at {cpu_percent}%")
            
            if memory_percent > 90:
                await self._send_system_alert("HIGH", "High Memory Usage", f"Memory usage at {memory_percent}%")
            
            if disk_percent > 95:
                await self._send_system_alert("CRITICAL", "Critical Disk Usage", f"Disk usage at {disk_percent}%")
                
        except ImportError:
            logger.warning("psutil not available for resource monitoring")
        except Exception as e:
            logger.error(f"Resource monitoring error: {str(e)}")
    
    async def _send_system_alert(self, priority: str, title: str, message: str):
        """Send system-level alert"""
        
        try:
            # Use execution agent's alert system
            if 'execution' in self.coordinators:
                alert_agent = self.coordinators['execution'].agents.get('alerts')
                if alert_agent:
                    # Create dummy strategy for alert system
                    from .execution_agents import TradingStrategy, StrategyType
                    
                    dummy_strategy = TradingStrategy(
                        token_address="SYSTEM",
                        chain="SYSTEM",
                        strategy_type=StrategyType.MONITOR,
                        confidence=1.0,
                        position_size=0.0,
                        entry_conditions=[],
                        exit_conditions=[],
                        risk_management={},
                        expected_return=0.0,
                        max_drawdown=0.0,
                        time_horizon="immediate",
                        reasoning=f"System alert: {title}",
                        created_at=datetime.now(timezone.utc)
                    )
                    
                    # Send alert
                    await alert_agent.generate_and_send_alert(
                        dummy_strategy,
                        {"final_consensus": {"risk_score": 0.0, "status": "system_alert"}}
                    )
            
        except Exception as e:
            logger.error(f"System alert failed: {str(e)}")
    
    async def get_system_health(self) -> Dict:
        """Get comprehensive system health status"""
        
        try:
            # Check Redis connectivity
            await self.redis_client.ping()
            redis_status = "healthy"
        except:
            redis_status = "unhealthy"
        
        # Check queue lengths
        queue_lengths = {
            "analysis_queue": await self.redis_client.llen("token_analysis_queue"),
            "verification_queue": await self.redis_client.llen("token_verification_queue"),
            "execution_queue": await self.redis_client.llen("token_execution_queue")
        }
        
        # Check recent errors
        recent_errors = await self.redis_client.llen("system_errors")
        
        # Calculate overall status
        issues = []
        
        if redis_status == "unhealthy":
            issues.append("Redis connectivity issues")
        
        if any(length > 500 for length in queue_lengths.values()):
            issues.append("Queue bottlenecks detected")
        
        if recent_errors > 10:
            issues.append("High error rate detected")
        
        # Determine overall status
        if not issues:
            overall_status = "healthy"
        elif len(issues) == 1:
            overall_status = "degraded" 
        else:
            overall_status = "critical"
        
        return {
            "overall_status": overall_status,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "redis_status": redis_status,
            "queue_lengths": queue_lengths,
            "recent_errors": recent_errors,
            "issues": issues,
            "uptime_hours": (datetime.now(timezone.utc) - self.system_start_time).total_seconds() / 3600,
            "performance_metrics": self.performance_metrics.__dict__
        }
    
    async def get_system_metrics(self) -> Dict:
        """Get detailed system performance metrics"""
        
        metrics_data = await self.redis_client.get("system_metrics")
        if metrics_data:
            return json.loads(metrics_data)
        
        return {
            "metrics": self.performance_metrics.__dict__,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

# Production API Manager
class APIManager:
    """
    Manages API connections and rate limiting for all external services
    """
    
    def __init__(self):
        self.api_keys = {
            'openrouter': os.getenv('OPENROUTER_API_KEY'),
            'helius': os.getenv('API_HELIUS_API_KEY'),
            'infura': os.getenv('API_INFURA_API_KEY'),
            'moralis': os.getenv('API_MORALIS_API_KEY'),
            'birdeye': os.getenv('API_BIRDEYE_API_KEY'),
            'apify': os.getenv('API_APIFY_API_KEY'),
            'etherscan': os.getenv('API_ETHERSCAN_API_KEY'),
            'tokensniffer': os.getenv('API_TOKENSNIFFER_KEY')
        }
        
        # Rate limiting state
        self.rate_limits = {}
        self.request_counts = {}
        
    async def initialize(self):
        """Initialize API manager"""
        
        # Validate required API keys
        required_keys = ['openrouter', 'helius', 'infura']
        missing_keys = [key for key in required_keys if not self.api_keys[key]]
        
        if missing_keys:
            raise ValueError(f"Missing required API keys: {missing_keys}")
        
        logger.info("API Manager initialized successfully")
    
    async def shutdown(self):
        """Shutdown API manager"""
        logger.info("API Manager shutdown complete")

# Main entry point
async def main():
    """
    Main entry point for the MemeGuard Pro Agent System
    """
    
    # Initialize system manager
    system_manager = AgentSystemManager()
    
    try:
        # Initialize all components
        await system_manager.initialize()
        
        # Start the system
        await system_manager.start()
        
    except KeyboardInterrupt:
        logger.info("Shutdown requested by user")
    except Exception as e:
        logger.error(f"System failed: {str(e)}")
        raise
    finally:
        # Ensure clean shutdown
        await system_manager.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
