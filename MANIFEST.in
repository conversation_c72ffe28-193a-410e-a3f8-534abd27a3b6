# MemeGuard Pro - Package Manifest
# Include additional files in the distribution package

include README.md
include LICENSE
include requirements.txt
include .env.example
include system_architecture.md
include blueprint_prd.md

# Include configuration files
recursive-include .kiro *.md *.json *.yaml *.yml
recursive-include config *.json *.yaml *.yml *.toml

# Include documentation
recursive-include docs *.md *.rst *.txt *.py

# Include test files
recursive-include tests *.py *.json *.yaml *.yml

# Include agent templates and prompts
recursive-include agents/templates *.txt *.md *.json
recursive-include agents/prompts *.txt *.md

# Exclude development and build files
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .git*
global-exclude .DS_Store
global-exclude *.so
global-exclude .env
prune build
prune dist
prune *.egg-info