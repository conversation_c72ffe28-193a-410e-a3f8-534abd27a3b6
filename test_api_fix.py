#!/usr/bin/env python3
"""Test script for the corrected API endpoints"""

import time
import asyncio
import aiohttp
import logging
from typing import List, Dict

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_new_pairs_dexscreener(chain: str) -> List[Dict]:
    """
    Fixed DEXScreener API function using search-based approach
    """
    import aiohttp
    import asyncio
    import time
    
    async def fetch_pairs():
        # Use search approach since direct "new pairs" endpoint doesn't exist
        search_queries = [
            "meme",
            "launch", 
            "new",
            "token",
            "sol"
        ]
        
        current_time = time.time()
        all_results = []
        
        async with aiohttp.ClientSession() as session:
            for query in search_queries:
                try:
                    url = f"https://api.dexscreener.com/latest/dex/search?q={query}"
                    
                    async with session.get(
                        url,
                        timeout=aiohttp.ClientTimeout(total=15)
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            pairs = data.get('pairs', [])
                            
                            # Filter for recent pairs on specified chain
                            for pair in pairs:
                                if pair.get('chainId') == chain:
                                    # Check if pair is recent (based on pairCreatedAt or volume)
                                    created_at = pair.get('pairCreatedAt')
                                    if created_at:
                                        try:
                                            created_timestamp = int(created_at) / 1000
                                            age_hours = (current_time - created_timestamp) / 3600
                                            if age_hours < 24:  # Last 24 hours
                                                pair['age_hours'] = age_hours
                                                all_results.append(pair)
                                        except:
                                            pass
                                    
                                    # Also include pairs with significant volume (might be new)
                                    volume_24h = pair.get('volume', {}).get('h24', 0)
                                    if volume_24h > 1000:  # Minimum volume threshold
                                        all_results.append(pair)
                        
                        else:
                            logger.warning(f"DEXScreener search error {response.status} for query: {query}")
                            
                except asyncio.TimeoutError:
                    logger.error(f"DEXScreener timeout for query: {query}")
                except Exception as e:
                    logger.error(f"DEXScreener error for query {query}: {str(e)}")
                
                await asyncio.sleep(0.5)  # Rate limiting
        
        # Sort by creation time and limit results
        if all_results:
            # Remove duplicates by address
            seen_addresses = set()
            unique_results = []
            for result in all_results:
                address = result.get('baseToken', {}).get('address', '')
                if address and address not in seen_addresses:
                    seen_addresses.add(address)
                    unique_results.append(result)
            
            logger.info(f"DEXScreener found {len(unique_results)} unique tokens")
            return unique_results[:20]  # Return top 20
        else:
            logger.info("DEXScreener: No new tokens found")
            return []
    
    return asyncio.run(fetch_pairs())

def test_api_endpoints():
    """Test the corrected API endpoints"""
    
    print("=" * 50)
    print("Testing Corrected API Endpoints")
    print("=" * 50)
    
    # Test DEXScreener
    print("\n1. Testing DEXScreener search-based detection...")
    try:
        results = get_new_pairs_dexscreener("solana")
        print(f"   ✅ Success: Found {len(results)} tokens")
        
        if results:
            sample = results[0]
            print(f"   📊 Sample result:")
            print(f"      Symbol: {sample.get('baseToken', {}).get('symbol', 'N/A')}")
            print(f"      Address: {sample.get('baseToken', {}).get('address', 'N/A')}")
            print(f"      Chain: {sample.get('chainId', 'N/A')}")
            print(f"      Age: {sample.get('age_hours', 'N/A')} hours")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    # Test direct DEXScreener endpoints
    print("\n2. Testing direct DEXScreener API endpoints...")
    
    test_urls = [
        "https://api.dexscreener.com/latest/dex/search?q=solana",
        "https://api.dexscreener.com/latest/dex/tokens/So11111111111111111111111111111111111111112",  # WSOL
    ]
    
    import requests
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ {url}")
                print(f"      Response keys: {list(data.keys())}")
            else:
                print(f"   ❌ {url} - Status: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {url} - Error: {str(e)}")

if __name__ == "__main__":
    test_api_endpoints()
