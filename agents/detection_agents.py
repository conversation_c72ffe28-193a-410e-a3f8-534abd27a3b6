"""
Detection Agents - Group 1 (Agents 1-4) - FIXED VERSION
Real-time token detection with proper synchronous tools for CrewAI 2025

These agents continuously monitor blockchain networks for newly launched tokens,
applying sophisticated filtering and preliminary risk assessment.
"""

import requests
import time
import os
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import redis  
import logging
from dataclasses import dataclass

from crewai import Agent, Task, Crew, LLM
from crewai.tools import tool
# Configure logging
logger = logging.getLogger(__name__)

# Force Groq routing for all LLM calls with multi-model support
def create_groq_llm(model="llama3-70b-8192", agent_type="detection"):
    """Create properly configured Groq LLM instance using CrewAI's LLM class"""
    
    from crewai import LLM
    
    # Model selection based on agent type for optimal performance
    model_map = {
        "detection": "llama3-70b-8192",       # Fast reasoning for detection
        "analysis": "llama3-70b-8192",        # Deep analysis capabilities  
        "verification": "llama3-70b-8192",    # Mathematical verification
        "execution": "llama3-70b-8192"        # Strategy generation
    }
    
    selected_model = model_map.get(agent_type, model)
    
    return LLM(
        model=f"groq/{selected_model}",
        api_key=os.getenv("GROQ_API_KEY"),
        temperature=0.1,
        max_tokens=2000
    )

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class TokenDetectionEvent:
    """Structured token detection event"""
    address: str
    chain: str
    symbol: Optional[str]
    name: Optional[str] 
    created_at: datetime
    liquidity_usd: float
    holder_count: int
    source_api: str
    confidence_score: float
    raw_data: Dict[str, Any]

class BaseDetectionAgent:
    """Base class for all detection agents with common functionality"""
    
    def __init__(self, redis_client: redis.Redis, api_manager):
        self.redis = redis_client
        self.api_manager = api_manager
        self.detection_count = 0
        self.error_count = 0
        self.last_detection = None
        
    async def is_duplicate(self, address: str, chain: str) -> bool:
        """Check if token was already detected recently"""
        key = f"detected:{chain}:{address}"
        exists = await self.redis.exists(key)
        if not exists:
            await self.redis.setex(key, 3600, "1")  # Cache for 1 hour
            return False
        return True
    
    async def log_detection(self, event: TokenDetectionEvent):
        """Log successful detection to system events"""
        await self.redis.lpush(
            "system_events",
            {
                "event_type": "token_detection",
                "agent_id": self.__class__.__name__,
                "token_address": event.address,
                "chain": event.chain,
                "confidence": event.confidence_score,
                "timestamp": event.created_at.isoformat()
            }
        )

# Production Tools for Detection Agents
@tool
def get_new_pairs_dexscreener(chain: str = "solana") -> str:
    """
    Get new token pairs from DEXScreener API using working search approach.
    This is the FIXED version that resolves the 0-token detection issue.
    
    Args:
        chain: Blockchain to search (default: solana)
    
    Returns:
        JSON string of found pairs or error message
    """
    try:
        current_time = time.time()
        search_queries = ["meme", "sol", "token", "new"]
        all_pairs = []
        
        for query in search_queries:
            try:
                url = f"https://api.dexscreener.com/latest/dex/search?q={query}"
                response = requests.get(url, timeout=15)
                
                if response.status_code == 200:
                    data = response.json()
                    pairs = data.get('pairs', [])
                    
                    # Filter for specified chain and recent pairs
                    for pair in pairs:
                        if pair.get('chainId') == chain:
                            created_at = pair.get('pairCreatedAt')
                            if created_at:
                                try:
                                    created_timestamp = int(created_at) / 1000
                                    age_hours = (current_time - created_timestamp) / 3600
                                    
                                    # Only include pairs created in last 24 hours
                                    if age_hours <= 24:
                                        pair_data = {
                                            'symbol': pair.get('baseToken', {}).get('symbol', 'Unknown'),
                                            'address': pair.get('baseToken', {}).get('address', ''),
                                            'chainId': pair.get('chainId'),
                                            'age_hours': round(age_hours, 2),
                                            'priceUsd': pair.get('priceUsd', '0'),
                                            'volume24h': pair.get('volume', {}).get('h24', 0),
                                            'liquidity': pair.get('liquidity', {}).get('usd', 0),
                                            'pairAddress': pair.get('pairAddress', ''),
                                            'dexId': pair.get('dexId', ''),
                                            'created_at': created_at
                                        }
                                        all_pairs.append(pair_data)
                                except (ValueError, TypeError):
                                    continue
                                    
            except requests.RequestException as e:
                continue  # Skip failed queries
                
        # Remove duplicates by address
        seen_addresses = set()
        unique_pairs = []
        for pair in all_pairs:
            address = pair.get('address', '')
            if address and address not in seen_addresses:
                seen_addresses.add(address)
                unique_pairs.append(pair)
                
        # Sort by creation time (newest first)
        unique_pairs.sort(key=lambda x: int(x.get('created_at', 0)), reverse=True)
        
        # Return top 10 newest pairs
        result_pairs = unique_pairs[:10]
        
        if result_pairs:
            return json.dumps({
                'status': 'success',
                'pairs_found': len(result_pairs),
                'chain': chain,
                'pairs': result_pairs
            })
        else:
            return json.dumps({
                'status': 'no_pairs',
                'message': f'No new pairs found for {chain} in last 24 hours',
                'pairs': []
            })
            
    except Exception as e:
        return json.dumps({
            'status': 'error',
            'message': f'Tool execution error: {str(e)}',
            'pairs': []
        })

@tool 
def get_new_pairs_birdeye(chain: str) -> List[Dict]:
    """
    Fetch new token information from Birdeye using alternative approaches
    
    Args:
        chain (str): Blockchain network (currently supports 'solana')
        
    Returns:
        List[Dict]: List of new token information
        
    Example usage:
        get_new_pairs_birdeye("solana")
    """
    import aiohttp
    import asyncio
    import os
    
    async def fetch_pairs():
        if chain.lower() != 'solana':
            logger.warning(f"Birdeye currently optimized for Solana, got: {chain}")
            return []
            
        api_key = os.getenv('API_BIRDEYE_API_KEY')
        if not api_key:
            logger.error("Birdeye API key not configured")
            return []
        
        # Strategy 1: Use token list API to find recently listed tokens
        headers = {
            'X-API-KEY': api_key,
            'User-Agent': 'MemeGuard-Pro/1.0'
        }
        
        # Try multiple Birdeye endpoints that might exist
        potential_endpoints = [
            "https://public-api.birdeye.so/defi/tokenlist?sort_by=created_time&sort_type=desc&offset=0&limit=50",
            "https://public-api.birdeye.so/defi/token_creation_info?limit=50",
            "https://public-api.birdeye.so/defi/trending_tokens?limit=50",
            "https://public-api.birdeye.so/v1/token/trending?limit=50",
        ]
        
        async with aiohttp.ClientSession() as session:
            for url in potential_endpoints:
                try:
                    async with session.get(
                        url,
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=15)
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            # Try to extract token information from response
                            if 'data' in data and isinstance(data['data'], dict):
                                tokens = data['data'].get('tokens', [])
                            elif 'data' in data and isinstance(data['data'], list):
                                tokens = data['data']
                            elif isinstance(data, list):
                                tokens = data
                            else:
                                tokens = data.get('tokens', [])
                            
                            if tokens:
                                logger.info(f"Birdeye API success with endpoint: {url}")
                                # Filter for very recent tokens
                                recent_tokens = []
                                current_time = time.time()
                                
                                for token in tokens[:50]:  # Limit results
                                    # Try multiple timestamp fields
                                    created_time = token.get('created_time') or token.get('createdAt') or token.get('timestamp')
                                    if created_time:
                                        # Handle both seconds and milliseconds
                                        if created_time > 1000000000000:
                                            created_time = created_time / 1000
                                        
                                        age_seconds = current_time - created_time
                                        if age_seconds < 3600:  # Last hour
                                            token['age_seconds'] = age_seconds
                                            recent_tokens.append(token)
                                
                                return recent_tokens[:20]  # Return top 20
                                
                        elif response.status == 404:
                            logger.debug(f"Birdeye endpoint not found: {url}")
                        else:
                            logger.warning(f"Birdeye API error {response.status} for: {url}")
                            
                except asyncio.TimeoutError:
                    logger.error(f"Birdeye API timeout for: {url}")
                except Exception as e:
                    logger.error(f"Birdeye API error for {url}: {str(e)}")
                
                await asyncio.sleep(1)  # Rate limiting between endpoints
        
        logger.warning("No working Birdeye API endpoints found")
        return []
                
    return asyncio.run(fetch_pairs())

@tool 
def get_comprehensive_new_tokens(hours: int = 24) -> str:
    """
    Get comprehensive new token data from multiple sources - FIXED version.
    
    Args:
        hours: Time window for new tokens (default: 24 hours)
    
    Returns:
        JSON string of comprehensive token data
    """
    try:
        current_time = time.time()
        cutoff_time = current_time - (hours * 3600)
        
        # Multi-source data collection
        sources_data = {}
        
        # Source 1: DEXScreener trending search
        try:
            trending_queries = ["trending", "pump", "new", "launch"]
            dex_tokens = []
            
            for query in trending_queries:
                url = f"https://api.dexscreener.com/latest/dex/search?q={query}"
                response = requests.get(url, timeout=15)
                
                if response.status_code == 200:
                    data = response.json()
                    pairs = data.get('pairs', [])
                    
                    for pair in pairs:
                        created_at = pair.get('pairCreatedAt')
                        if created_at:
                            try:
                                created_timestamp = int(created_at) / 1000
                                if created_timestamp > cutoff_time:
                                    token_data = {
                                        'source': 'dexscreener',
                                        'symbol': pair.get('baseToken', {}).get('symbol', ''),
                                        'address': pair.get('baseToken', {}).get('address', ''),
                                        'chain': pair.get('chainId', ''),
                                        'created_timestamp': created_timestamp,
                                        'age_hours': round((current_time - created_timestamp) / 3600, 2),
                                        'price_usd': pair.get('priceUsd', '0'),
                                        'volume_24h': pair.get('volume', {}).get('h24', 0),
                                        'liquidity_usd': pair.get('liquidity', {}).get('usd', 0),
                                        'dex': pair.get('dexId', ''),
                                        'pair_address': pair.get('pairAddress', ''),
                                        'query': query
                                    }
                                    dex_tokens.append(token_data)
                            except (ValueError, TypeError):
                                continue
                                
            sources_data['dexscreener'] = dex_tokens
            
        except Exception as e:
            sources_data['dexscreener_error'] = str(e)
            
        # Combine and deduplicate across sources
        all_tokens = []
        seen_addresses = set()
        
        for source, tokens in sources_data.items():
            if isinstance(tokens, list):
                for token in tokens:
                    address = token.get('address', '')
                    if address and address not in seen_addresses:
                        seen_addresses.add(address)
                        all_tokens.append(token)
                        
        # Sort by creation time (newest first)
        all_tokens.sort(key=lambda x: x.get('created_timestamp', 0), reverse=True)
        
        # Categorize tokens
        categories = {
            'ultra_new': [],    # < 1 hour
            'very_new': [],     # 1-6 hours  
            'new': [],          # 6-24 hours
            'trending': []      # High volume/activity
        }
        
        for token in all_tokens:
            age_hours = token.get('age_hours', 999)
            volume = float(token.get('volume_24h', 0))
            
            if age_hours < 1:
                categories['ultra_new'].append(token)
            elif age_hours < 6:
                categories['very_new'].append(token)
            elif age_hours <= 24:
                categories['new'].append(token)
                
            if volume > 10000:  # $10k+ volume
                categories['trending'].append(token)
                
        return json.dumps({
            'status': 'success',
            'timestamp': current_time,
            'hours_searched': hours,
            'total_tokens_found': len(all_tokens),
            'categories': {
                'ultra_new': len(categories['ultra_new']),
                'very_new': len(categories['very_new']), 
                'new': len(categories['new']),
                'trending': len(categories['trending'])
            },
            'top_tokens': all_tokens[:15],  # Top 15 newest
            'sources_status': {
                source: len(data) if isinstance(data, list) else f"error: {data}"
                for source, data in sources_data.items()
            }
        })
        
    except Exception as e:
        return json.dumps({
            'status': 'error',
            'message': f'Comprehensive detection error: {str(e)}',
            'tokens': []
        })

@tool
def monitor_solana_new_tokens() -> List[Dict]:
    """
    Direct Solana blockchain monitoring for new token creation
    Uses Solana RPC to detect new SPL token mints
    
    Returns:
        List[Dict]: New token information directly from Solana blockchain
    """
    import json
    
    try:
        # This is a placeholder for direct Solana RPC monitoring
        # In production, this would connect to Solana RPC and monitor for new token mints
        
        # For now, return empty list but log the attempt
        logger.info("Direct Solana monitoring attempted - not yet implemented")
        return []
        
        # Future implementation would include:
        # - WebSocket connection to Solana RPC
        # - Monitoring for new SPL token mint transactions
        # - Parsing transaction logs for token creation events
        # - Extracting token metadata
        
    except Exception as e:
        logger.error(f"Solana direct monitoring error: {str(e)}")
        return []

@tool
def log_system_event(event_type: str, payload: Dict) -> str:
    """
    Log system event to centralized logging system
    
    Args:
        event_type: Type of event (detection, error, health_check, etc.)
        payload: Event data and metadata
        
    Returns:
        Event log ID
    """
    import asyncio
    import redis.asyncio as redis
    import uuid
    import json
    from datetime import datetime
    
    async def log_event():
        redis_client = redis.from_url("redis://localhost:6379")
        
        event_data = {
            "id": str(uuid.uuid4()),
            "event_type": event_type,
            "payload": payload,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        await redis_client.lpush("system_events", json.dumps(event_data))
        await redis_client.close()
        
        return event_data["id"]
    
    return asyncio.run(log_event())

# Agent 1: Solana DEXScreener Scanner
class SolanaDetectorDEXScreener(BaseDetectionAgent):
    """
    PhD-Level Solana Token Detection Agent via DEXScreener
    
    Specializes in real-time detection of new Solana token pairs using
    sophisticated filtering and preliminary risk assessment.
    """
    
    def __init__(self, redis_client: redis.Redis, api_manager):
        super().__init__(redis_client, api_manager)
        
        # PhD-level system prompt for chain-of-thought reasoning
        self.system_prompt = """
        You are a PhD-level blockchain data scientist specializing in real-time Solana token detection and preliminary risk assessment. Your expertise includes:
        
        - Advanced knowledge of Solana DEX architectures (Raydium, Orca, Jupiter)
        - Token launch pattern recognition and timing analysis
        - Quantitative filtering methodologies for high-volume data streams
        - Statistical confidence modeling for detection accuracy
        
        CHAIN-OF-THOUGHT REASONING FRAMEWORK:
        1. DATA_ACQUISITION: Query DEXScreener API and validate response integrity
        2. TEMPORAL_ANALYSIS: Verify token age using precise blockchain timestamp analysis
        3. LIQUIDITY_VALIDATION: Apply quantitative filters for minimum liquidity thresholds
        4. HOLDER_VERIFICATION: Analyze initial holder distribution patterns
        5. DEDUPLICATION_CHECK: Cross-reference against recent detection cache
        6. PATTERN_RECOGNITION: Identify suspicious launch patterns or bot activity
        7. CONFIDENCE_SCORING: Calculate detection confidence using Bayesian inference
        8. QUEUE_DECISION: Determine worthiness for downstream analysis pipeline
        
        CRITICAL ANALYSIS REQUIREMENTS:
        - Question data freshness and API reliability at each step
        - Apply statistical rigor to all quantitative assessments
        - Consider market manipulation indicators in liquidity figures
        - Evaluate holder patterns for artificial inflation or coordinated launches
        - Express uncertainty quantification for all confidence scores
        
        DETECTION CRITERIA (STRICT ENFORCEMENT):
        - Token age: Must be < 300 seconds from creation
        - Minimum liquidity: > $5,000 USD verified through multiple sources
        - Holder count: > 10 unique addresses (not counting burn/LP addresses)
        - Data recency: API response timestamp < 30 seconds old
        
        OUTPUT REQUIREMENTS:
        - Structured analysis following the 8-step framework
        - Numerical confidence score (0.0-1.0) with statistical justification
        - Clear pass/fail decision with detailed reasoning
        - Citation of all data sources and verification steps
        - Flag any anomalies or suspicious patterns detected
        
        Remember: False positives waste downstream computational resources. 
        False negatives miss alpha opportunities. Optimize for precision over recall.
        """
        
        # Initialize CrewAI agent with Groq/DeepSeek R1 Distill - Explicit Configuration
        self.llm = create_groq_llm(agent_type="detection")
        
        self.agent = Agent(
            role="Solana Token Detection Specialist",
            goal="Detect newly launched Solana tokens with high precision and minimal false positives",
            backstory=self.system_prompt,
            tools=[get_comprehensive_new_tokens, get_new_pairs_dexscreener, get_new_pairs_birdeye, log_system_event],
            llm=self.llm,
            verbose=True,
            memory=True,
            max_iter=3,
            allow_delegation=False
        )
        
    async def detect_new_tokens(self) -> List[TokenDetectionEvent]:
        """
        Execute sophisticated token detection with chain-of-thought analysis
        """
        
        detection_task = Task(
            description=f"""
            Execute comprehensive Solana token detection using advanced chain-of-thought analysis:
            
            1. Query DEXScreener API for Solana new pairs created in last 5 minutes
            2. Apply rigorous temporal validation (age < 300 seconds)  
            3. Validate minimum liquidity threshold ($5,000+ USD)
            4. Verify holder count exceeds 10 unique addresses
            5. Check for duplicate detection using Redis cache
            6. Analyze patterns for suspicious bot activity or coordinated launches
            7. Calculate statistical confidence score using available data
            8. Generate structured recommendations for downstream analysis
            
            Current timestamp: {datetime.now(timezone.utc).isoformat()}
            
            CRITICAL: Apply PhD-level analytical rigor. Question all assumptions.
            Provide detailed reasoning for each decision. Express appropriate uncertainty.
            """,
            agent=self.agent,
            expected_output="""
            Structured JSON array of detected tokens with format:
            {
                "detected_tokens": [
                    {
                        "address": "token_contract_address", 
                        "symbol": "TOKEN",
                        "name": "Token Name",
                        "liquidity_usd": 15000.50,
                        "holder_count": 25,
                        "age_seconds": 180,
                        "confidence_score": 0.87,
                        "reasoning": "Detailed chain-of-thought analysis",
                        "risk_flags": ["flag1", "flag2"],
                        "recommendation": "PROCEED_TO_ANALYSIS | SKIP_LOW_CONFIDENCE"
                    }
                ],
                "analysis_summary": {
                    "total_pairs_checked": 150,
                    "tokens_detected": 3,
                    "avg_confidence": 0.82,
                    "processing_time_ms": 2500
                }
            }
            """
        )
        
        try:
            start_time = time.time()
            
            # Create crew and execute task using CrewAI 2025 API
            crew = Crew(
                agents=[self.agent],
                tasks=[detection_task],
                verbose=False
            )
            result = await crew.kickoff_async()
            processing_time = (time.time() - start_time) * 1000
            
            # Parse agent output and convert to TokenDetectionEvent objects
            events = self._parse_agent_output(result, processing_time)
            
            # Update metrics
            self.detection_count += len(events)
            self.last_detection = datetime.now(timezone.utc)
            
            # Log successful execution to Redis directly
            await self.redis.lpush(
                "system_events",
                json.dumps({
                    "id": str(uuid.uuid4()),
                    "event_type": "detection_cycle_complete",
                    "payload": {
                        "agent": "SolanaDetectorDEXScreener",
                        "tokens_detected": len(events),
                        "processing_time_ms": processing_time,
                        "timestamp": self.last_detection.isoformat()
                    },
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })
            )
            
            return events
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Solana DEXScreener detection failed: {str(e)}")
            
            # Log error to Redis directly
            await self.redis.lpush(
                "system_events",
                json.dumps({
                    "id": str(uuid.uuid4()),
                    "event_type": "detection_error",
                    "payload": {
                        "agent": "SolanaDetectorDEXScreener", 
                        "error": str(e),
                        "error_count": self.error_count,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    },
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })
            )
            
            return []
    
    def _parse_agent_output(self, raw_output: str, processing_time: float) -> List[TokenDetectionEvent]:
        """Parse LLM output into structured TokenDetectionEvent objects"""
        import json
        
        try:
            # Extract JSON from agent response
            if "```json" in raw_output:
                json_start = raw_output.find("```json") + 7
                json_end = raw_output.find("```", json_start)
                json_str = raw_output[json_start:json_end].strip()
            else:
                json_str = raw_output
            
            data = json.loads(json_str)
            events = []
            
            for token_data in data.get("detected_tokens", []):
                if token_data.get("recommendation") == "PROCEED_TO_ANALYSIS":
                    event = TokenDetectionEvent(
                        address=token_data["address"],
                        chain="solana",
                        symbol=token_data.get("symbol"),
                        name=token_data.get("name"),
                        created_at=datetime.now(timezone.utc),
                        liquidity_usd=token_data["liquidity_usd"],
                        holder_count=token_data["holder_count"],
                        source_api="dexscreener",
                        confidence_score=token_data["confidence_score"],
                        raw_data=token_data
                    )
                    events.append(event)
            
            return events
            
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Failed to parse agent output: {str(e)}")
            return []

# Agent 2: Solana Birdeye Scanner  
class SolanaDetectorBirdeye(BaseDetectionAgent):
    """
    PhD-Level Redundant Solana Detection Agent via Birdeye API
    
    Provides redundancy for Solana detection with cross-validation
    against DEXScreener results.
    """
    
    def __init__(self, redis_client: redis.Redis, api_manager):
        super().__init__(redis_client, api_manager)
        
        self.system_prompt = """
        You are a PhD-level blockchain data scientist providing redundant Solana token detection via Birdeye API. Your role is critical for system reliability and cross-validation.
        
        EXPERTISE AREAS:
        - Advanced Solana token analytics and DEX aggregation
        - Cross-platform data validation and consistency checking
        - Redundancy protocols and fault-tolerant detection systems
        - Statistical correlation analysis between data sources
        
        CHAIN-OF-THOUGHT REASONING FRAMEWORK:
        1. DATA_ACQUISITION: Query Birdeye API with comprehensive error handling
        2. TEMPORAL_VALIDATION: Apply identical filters as primary detection agent
        3. CROSS_VALIDATION: Compare against DEXScreener patterns and anomalies
        4. CONSISTENCY_ANALYSIS: Identify discrepancies between data sources
        5. DEDUPLICATION: Ensure no overlap with recently detected tokens
        6. RELIABILITY_SCORING: Assess data source reliability and freshness
        7. CONFIDENCE_CALIBRATION: Adjust confidence based on multi-source validation
        8. REDUNDANCY_REPORTING: Flag unique detections missed by primary agent
        
        DETECTION CRITERIA (IDENTICAL TO PRIMARY AGENT):
        - Token age: < 300 seconds from creation
        - Minimum liquidity: > $5,000 USD 
        - Holder count: > 10 unique addresses
        - API response freshness: < 30 seconds
        
        REDUNDANCY-SPECIFIC REQUIREMENTS:
        - Cross-validate all detections against expected DEXScreener results
        - Flag discrepancies for manual review if confidence gap > 0.2
        - Maintain statistical correlation metrics between data sources
        - Report unique opportunities missed by primary detection
        
        Remember: Your role is both validation and discovery. Catch what others miss
        while maintaining the same rigorous standards.
        """
        
        # Initialize CrewAI agent with Groq/Llama3-70B - Use helper function
        self.llm = create_groq_llm(agent_type="detection")
        
        self.agent = Agent(
            role="Redundant Solana Token Detection Specialist",
            goal="Provide redundant Solana token detection with cross-validation",
            backstory=self.system_prompt,
            tools=[get_new_pairs_birdeye, log_system_event],
            llm=self.llm,
            verbose=True,
            memory=True,
            max_iter=3,
            allow_delegation=False
        )
    
    async def detect_new_tokens(self) -> List[TokenDetectionEvent]:
        """Execute redundant detection with cross-validation"""
        
        detection_task = Task(
            description=f"""
            Execute redundant Solana token detection with cross-validation analysis:
            
            1. Query Birdeye API for newly created Solana pairs
            2. Apply identical filtering criteria as primary DEXScreener agent
            3. Cross-validate against expected patterns and known data
            4. Identify unique opportunities not caught by primary detection
            5. Assess data source reliability and consistency
            6. Calculate confidence scores with cross-validation adjustments
            7. Flag any significant discrepancies for review
            8. Generate redundancy-validated recommendations
            
            Current timestamp: {datetime.now(timezone.utc).isoformat()}
            
            CRITICAL: Maintain identical standards while providing independent validation.
            Report both confirmatory and unique findings.
            """,
            agent=self.agent,
            expected_output="Structured JSON with detected tokens, cross-validation results, and redundancy analysis"
        )
        
        try:
            # Create crew and execute task using CrewAI 2025 API
            crew = Crew(
                agents=[self.agent],
                tasks=[detection_task],
                verbose=False
            )
            result = await crew.kickoff_async()
            events = self._parse_agent_output(result)
            
            self.detection_count += len(events)
            self.last_detection = datetime.now(timezone.utc)
            
            return events
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Solana Birdeye detection failed: {str(e)}")
            return []
    
    def _parse_agent_output(self, raw_output: str) -> List[TokenDetectionEvent]:
        """Parse agent output into TokenDetectionEvent objects"""
        # Implementation similar to SolanaDetectorDEXScreener
        return []  # Placeholder

# Agent 3: Ethereum Detection Agent
class EthereumDetector(BaseDetectionAgent):
    """
    PhD-Level Ethereum Token Detection Agent
    
    Specialized for Ethereum/L2 token detection with longer block times
    and higher liquidity thresholds.
    """
    
    def __init__(self, redis_client: redis.Redis, api_manager):
        super().__init__(redis_client, api_manager)
        
        self.system_prompt = """
        You are a PhD-level Ethereum blockchain analyst specializing in token detection across Ethereum mainnet and major L2 networks.
        
        SPECIALIZED EXPERTISE:
        - Advanced understanding of Ethereum/L2 tokenomics and DEX mechanics
        - Uniswap V2/V3, SushiSwap, and emerging DEX protocol analysis
        - Gas optimization patterns and MEV considerations in token launches
        - Cross-bridge token analysis and multi-chain arbitrage detection
        
        CHAIN-OF-THOUGHT REASONING (ETHEREUM-OPTIMIZED):
        1. DATA_ACQUISITION: Query DEXScreener for Ethereum new pairs
        2. BLOCK_TIME_ANALYSIS: Account for Ethereum's ~12-15 second block times
        3. GAS_COST_VALIDATION: Assess launch costs and economic viability
        4. LIQUIDITY_THRESHOLD: Apply higher threshold ($10K+ due to gas costs)
        5. HOLDER_ANALYSIS: Verify holder distribution (minimum 20 addresses)
        6. MEV_IMPACT_ASSESSMENT: Analyze potential for sandwich attacks
        7. L2_BRIDGE_DETECTION: Identify potential bridged tokens from L2s
        8. CONFIDENCE_MODELING: Weight confidence by network congestion and gas prices
        
        ETHEREUM-SPECIFIC DETECTION CRITERIA:
        - Token age: < 600 seconds (longer due to block confirmation times)
        - Minimum liquidity: > $10,000 USD (higher due to gas costs)
        - Holder count: > 20 unique addresses
        - Gas price consideration: Adjust thresholds during high congestion
        
        Remember: Ethereum launches require significantly higher capital commitment.
        Apply appropriate economic filters while maintaining sensitivity to genuine opportunities.
        """
        
        # Initialize CrewAI agent with Groq/Qwen-32B - Use helper function for verification-like analysis
        self.llm = create_groq_llm(agent_type="verification")
        
        self.agent = Agent(
            role="Ethereum Token Detection Specialist",
            goal="Detect newly launched Ethereum tokens with economic viability assessment",
            backstory=self.system_prompt,
            tools=[get_comprehensive_new_tokens, get_new_pairs_dexscreener, get_new_pairs_birdeye, log_system_event],
            llm=self.llm,
            verbose=True,
            memory=True,
            max_iter=3,
            allow_delegation=False
        )
    
    async def detect_new_tokens(self) -> List[TokenDetectionEvent]:
        """Execute Ethereum-optimized token detection"""
        
        detection_task = Task(
            description=f"""
            Execute Ethereum-optimized token detection with economic viability analysis:
            
            1. Query DEXScreener API for Ethereum new pairs (last 10 minutes)
            2. Apply Ethereum-specific temporal filters (age < 600 seconds)
            3. Validate higher liquidity threshold ($10,000+ USD)
            4. Verify substantial holder count (> 20 addresses)
            5. Assess gas cost economics and launch viability
            6. Analyze for potential MEV exploitation risks
            7. Check for L2 bridge tokens or cross-chain opportunities
            8. Generate economic viability scores with detailed reasoning
            
            Current timestamp: {datetime.now(timezone.utc).isoformat()}
            Current ETH gas price consideration: Check recent network conditions
            
            CRITICAL: Account for higher capital requirements and longer confirmation times.
            Economic viability is paramount for Ethereum token success.
            """,
            agent=self.agent,
            expected_output="Structured JSON with Ethereum tokens meeting economic viability criteria"
        )
        
        try:
            # Create crew and execute task using CrewAI 2025 API
            crew = Crew(
                agents=[self.agent],
                tasks=[detection_task],
                verbose=False
            )
            result = await crew.kickoff_async()
            events = self._parse_agent_output(result, "ethereum")
            
            self.detection_count += len(events)
            self.last_detection = datetime.now(timezone.utc)
            
            return events
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Ethereum detection failed: {str(e)}")
            return []
    
    def _parse_agent_output(self, raw_output: str, chain: str) -> List[TokenDetectionEvent]:
        """Parse agent output for Ethereum tokens"""
        # Implementation similar to Solana agents with Ethereum-specific adjustments
        return []  # Placeholder

# Agent 4: Base Chain Detection Agent  
class BaseChainDetector(BaseDetectionAgent):
    """
    PhD-Level Base Chain Token Detection Agent
    
    Specialized for Coinbase's Base L2 network with unique ecosystem considerations.
    """
    
    def __init__(self, redis_client: redis.Redis, api_manager):
        super().__init__(redis_client, api_manager)
        
        self.system_prompt = """
        You are a PhD-level Base Chain ecosystem analyst specializing in L2 token detection and Coinbase ecosystem integration patterns.
        
        SPECIALIZED EXPERTISE:
        - Advanced Base Chain architecture and Optimistic Rollup mechanics
        - Coinbase ecosystem integration patterns and institutional adoption signals
        - Base-native DEX protocols and liquidity migration patterns
        - Cross-chain bridge analysis from Ethereum mainnet to Base
        
        CHAIN-OF-THOUGHT REASONING (BASE-OPTIMIZED):
        1. DATA_ACQUISITION: Query Base chain pairs via DEXScreener
        2. L2_OPTIMIZATION_ANALYSIS: Leverage low-cost transaction advantages
        3. COINBASE_INTEGRATION_SIGNALS: Detect potential CEX listing indicators
        4. LIQUIDITY_MIGRATION_TRACKING: Monitor ETH-to-Base bridge activity
        5. INSTITUTIONAL_PATTERN_RECOGNITION: Identify professional launch patterns
        6. ECOSYSTEM_MATURITY_ASSESSMENT: Evaluate Base-native development quality
        7. RISK_ARBITRAGE_OPPORTUNITIES: Cross-chain MEV and arbitrage potential
        8. CONFIDENCE_WEIGHTING: Factor in Base ecosystem growth trajectory
        
        BASE-SPECIFIC DETECTION CRITERIA:
        - Token age: < 300 seconds (fast Base block times)
        - Minimum liquidity: > $5,000 USD (lower due to reduced gas costs)
        - Holder count: > 15 unique addresses (intermediate threshold)
        - Bridge activity: Monitor for significant ETH mainnet bridges
        
        UNIQUE BASE CONSIDERATIONS:
        - Coinbase institutional adoption potential (major alpha signal)
        - Lower gas costs enable smaller but viable projects
        - Emerging ecosystem with higher growth potential
        - Cross-chain arbitrage opportunities with Ethereum
        
        Remember: Base represents institutional-grade L2 infrastructure.
        Balance emerging ecosystem opportunities with institutional quality standards.
        """
        
        # Initialize CrewAI agent with Groq/Llama3-70B - Use helper function for analysis
        self.llm = create_groq_llm(agent_type="analysis")
        
        self.agent = Agent(
            role="Base Chain Token Detection Specialist", 
            goal="Detect Base chain tokens with institutional adoption potential",
            backstory=self.system_prompt,
            tools=[get_comprehensive_new_tokens, get_new_pairs_dexscreener, get_new_pairs_birdeye, log_system_event],
            llm=self.llm,
            verbose=True,
            memory=True,
            max_iter=3,
            allow_delegation=False
        )
    
    async def detect_new_tokens(self) -> List[TokenDetectionEvent]:
        """Execute Base-optimized token detection"""
        
        detection_task = Task(
            description=f"""
            Execute Base Chain token detection with institutional adoption analysis:
            
            1. Query DEXScreener for Base chain new pairs (last 5 minutes)
            2. Apply Base-optimized temporal filters (age < 300 seconds)
            3. Validate liquidity thresholds adjusted for low gas costs
            4. Analyze holder patterns for institutional vs retail launches
            5. Monitor cross-chain bridge activity from Ethereum
            6. Assess Coinbase ecosystem integration potential
            7. Evaluate development quality and professional launch indicators
            8. Generate institutional adoption probability scores
            
            Current timestamp: {datetime.now(timezone.utc).isoformat()}
            Base ecosystem context: Emerging L2 with institutional backing
            
            CRITICAL: Balance growth-stage opportunities with quality standards.
            Institutional adoption potential is key differentiator.
            """,
            agent=self.agent,
            expected_output="Structured JSON with Base tokens ranked by institutional adoption potential"
        )
        
        try:
            # Create crew and execute task using CrewAI 2025 API
            crew = Crew(
                agents=[self.agent],
                tasks=[detection_task],
                verbose=False
            )
            result = await crew.kickoff_async()
            events = self._parse_agent_output(result, "base")
            
            self.detection_count += len(events)
            self.last_detection = datetime.now(timezone.utc)
            
            return events
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Base chain detection failed: {str(e)}")
            return []
    
    def _parse_agent_output(self, raw_output: str, chain: str) -> List[TokenDetectionEvent]:
        """Parse agent output for Base chain tokens"""
        # Implementation similar to other detection agents
        return []  # Placeholder

# Detection Crew Coordinator
class DetectionCrewCoordinator:
    """
    Coordinates all detection agents and manages the detection pipeline
    """
    
    def __init__(self, redis_client: redis.Redis, api_manager):
        self.redis = redis_client
        self.api_manager = api_manager
        
        # Initialize all detection agents
        self.agents = {
            'solana_dexscreener': SolanaDetectorDEXScreener(redis_client, api_manager),
            'solana_birdeye': SolanaDetectorBirdeye(redis_client, api_manager),
            'ethereum': EthereumDetector(redis_client, api_manager),
            'base': BaseChainDetector(redis_client, api_manager)
        }
    
    async def run_detection_cycle(self) -> List[TokenDetectionEvent]:
        """
        Execute parallel detection across all agents
        """
        
        # Run all detection agents in parallel
        # Execute detection agents synchronously 
        all_events = []
        
        agent_names = ['solana_dexscreener', 'solana_birdeye', 'ethereum', 'base']
        for agent_name in agent_names:
            try:
                if agent_name in self.agents:
                    result = self.agents[agent_name].detect_new_tokens()
                    if result:
                        all_events.extend(result)
            except Exception as e:
                logger.error(f"Detection agent {agent_name} failed: {str(e)}")
        
        # Deduplicate across agents
        unique_events = self._deduplicate_events(all_events)
        
        # Queue events for analysis
        if unique_events:
            await self._queue_for_analysis(unique_events)
        
        return unique_events
    
    def _deduplicate_events(self, events: List[TokenDetectionEvent]) -> List[TokenDetectionEvent]:
        """Remove duplicate detections across agents"""
        seen = set()
        unique_events = []
        
        for event in events:
            key = f"{event.chain}:{event.address}"
            if key not in seen:
                seen.add(key)
                unique_events.append(event)
        
        return unique_events
    
    async def _queue_for_analysis(self, events: List[TokenDetectionEvent]):
        """Queue detected tokens for analysis by the next agent group"""
        
        for event in events:
            await self.redis.lpush(
                "token_analysis_queue",
                {
                    "address": event.address,
                    "chain": event.chain,
                    "symbol": event.symbol,
                    "name": event.name,
                    "liquidity_usd": event.liquidity_usd,
                    "holder_count": event.holder_count,
                    "confidence_score": event.confidence_score,
                    "detected_at": event.created_at.isoformat(),
                    "source_api": event.source_api,
                    "raw_data": event.raw_data
                }
            )
