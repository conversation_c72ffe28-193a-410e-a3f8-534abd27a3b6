"""
Unit tests for LLM router
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

from core.llm.router import <PERSON><PERSON><PERSON><PERSON>, RoutingStrategy, BudgetConfig, RoutingDecision
from core.llm.models import (
    LLMRequest, LLMResponse, ModelConfig, ModelProvider, ModelType,
    DEEPSEEK_R1_CONFIG, LLAMA_3_3_70B_CONFIG
)
from core.exceptions import APIError, RateLimitError


class TestBudgetConfig:
    """Test budget configuration"""
    
    def test_budget_exceeded(self):
        """Test budget exceeded check"""
        config = BudgetConfig(daily_budget_usd=10.0, weekly_budget_usd=50.0)
        
        # Within budget
        assert not config.is_budget_exceeded({'daily': 5.0, 'weekly': 25.0})
        
        # Daily budget exceeded
        assert config.is_budget_exceeded({'daily': 15.0, 'weekly': 25.0})
        
        # Weekly budget exceeded
        assert config.is_budget_exceeded({'daily': 5.0, 'weekly': 60.0})
    
    def test_emergency_threshold(self):
        """Test emergency threshold check"""
        config = BudgetConfig(
            daily_emergency_threshold=8.0,
            weekly_emergency_threshold=40.0
        )
        
        # Below threshold
        assert not config.is_emergency_threshold_reached({'daily': 5.0, 'weekly': 25.0})
        
        # Daily threshold reached
        assert config.is_emergency_threshold_reached({'daily': 9.0, 'weekly': 25.0})
        
        # Weekly threshold reached
        assert config.is_emergency_threshold_reached({'daily': 5.0, 'weekly': 45.0})


class TestLLMRouter:
    """Test LLM router functionality"""
    
    @pytest.fixture
    def router(self):
        """Create LLM router for testing"""
        return LLMRouter(
            openrouter_api_key="test_openrouter_key",
            groq_api_key="test_groq_key",
            routing_strategy=RoutingStrategy.COST_OPTIMIZED
        )
    
    @pytest.fixture
    def sample_request(self):
        """Create sample LLM request"""
        return LLMRequest(
            prompt="Test prompt",
            model_config=DEEPSEEK_R1_CONFIG,
            correlation_id="test-123",
            agent_id="test-agent"
        )
    
    @pytest.fixture
    def mock_response(self):
        """Create mock LLM response"""
        return LLMResponse(
            content="Test response",
            model_config=DEEPSEEK_R1_CONFIG,
            request_id="test-request",
            total_tokens=100,
            cost_usd=0.0,
            response_time_seconds=1.5
        )
    
    def test_initialization(self, router):
        """Test router initialization"""
        assert router.routing_strategy == RoutingStrategy.COST_OPTIMIZED
        assert isinstance(router.budget_config, BudgetConfig)
        assert ModelProvider.OPENROUTER in router.clients
        assert ModelProvider.GROQ in router.clients
        assert router.current_spend['daily'] == 0.0
    
    def test_model_health_tracking(self, router):
        """Test model health tracking"""
        model_name = "test-model"
        
        # Initial health should be healthy
        health = router._get_model_health(model_name)
        assert health['is_healthy'] is True
        assert health['failure_count'] == 0
        
        # Record failures
        for _ in range(3):
            router._update_model_health(model_name, success=False)
        
        health = router._get_model_health(model_name)
        assert health['is_healthy'] is False
        assert health['failure_count'] == 3
        
        # Record success should improve health
        router._update_model_health(model_name, success=True, response_time=1.0)
        health = router._get_model_health(model_name)
        assert health['failure_count'] == 2  # Decayed
        assert health['avg_response_time'] == 1.0
    
    def test_model_availability_check(self, router):
        """Test model availability checking"""
        # Healthy model should be available
        assert router._is_model_available(DEEPSEEK_R1_CONFIG)
        
        # Unhealthy model should not be available
        router._get_model_health(DEEPSEEK_R1_CONFIG.name)['is_healthy'] = False
        router._get_model_health(DEEPSEEK_R1_CONFIG.name)['last_failure'] = datetime.utcnow()
        assert not router._is_model_available(DEEPSEEK_R1_CONFIG)
        
        # Budget exceeded should block paid models
        router.current_spend['daily'] = 100.0  # Exceed budget
        assert not router._is_model_available(LLAMA_3_3_70B_CONFIG)  # Paid model
        assert router._is_model_available(DEEPSEEK_R1_CONFIG)  # Free model
    
    def test_routing_score_calculation(self, router, sample_request):
        """Test routing score calculation"""
        # Cost optimized should prefer free models
        router.routing_strategy = RoutingStrategy.COST_OPTIMIZED
        
        free_score = router._calculate_routing_score(DEEPSEEK_R1_CONFIG, sample_request)
        paid_score = router._calculate_routing_score(LLAMA_3_3_70B_CONFIG, sample_request)
        
        assert free_score > paid_score  # Free model should score higher
        
        # Performance optimized should prefer Groq
        router.routing_strategy = RoutingStrategy.PERFORMANCE_OPTIMIZED
        
        groq_score = router._calculate_routing_score(LLAMA_3_3_70B_CONFIG, sample_request)
        openrouter_score = router._calculate_routing_score(DEEPSEEK_R1_CONFIG, sample_request)
        
        # Groq should get performance bonus
        assert groq_score >= openrouter_score
    
    def test_model_selection(self, router, sample_request):
        """Test model selection and ranking"""
        models = router._select_models(ModelType.REASONING, sample_request)
        
        assert len(models) > 0
        assert all(model.model_type == ModelType.REASONING for model in models)
        
        # Should be sorted by score (best first)
        scores = [router._calculate_routing_score(model, sample_request) for model in models]
        assert scores == sorted(scores, reverse=True)
    
    def test_routing_decision(self, router, sample_request):
        """Test routing decision making"""
        decision = router.make_routing_decision(sample_request)
        
        assert isinstance(decision, RoutingDecision)
        assert decision.selected_model is not None
        assert isinstance(decision.fallback_models, list)
        assert decision.estimated_cost >= 0
        assert decision.estimated_latency > 0
        assert 0 <= decision.confidence_score <= 1
        assert decision.routing_reason is not None
    
    @patch('core.llm.clients.OpenRouterClient.generate')
    async def test_generate_success(self, mock_generate, router, sample_request, mock_response):
        """Test successful generation"""
        mock_generate.return_value = mock_response
        
        async with router:
            response = await router.generate(sample_request)
            
            assert response == mock_response
            assert router.current_spend['daily'] == mock_response.cost_usd
            mock_generate.assert_called_once()
    
    @patch('core.llm.clients.OpenRouterClient.generate')
    @patch('core.llm.clients.GroqClient.generate')
    async def test_generate_with_fallback(self, mock_groq_generate, mock_openrouter_generate, 
                                        router, sample_request, mock_response):
        """Test generation with fallback on failure"""
        # First client fails
        mock_openrouter_generate.side_effect = RateLimitError(
            "Rate limit exceeded", 
            api_service="openrouter"
        )
        
        # Second client succeeds
        mock_groq_generate.return_value = mock_response
        
        async with router:
            response = await router.generate(sample_request, fallback_on_error=True)
            
            assert response == mock_response
            mock_openrouter_generate.assert_called_once()
            mock_groq_generate.assert_called_once()
    
    @patch('core.llm.clients.OpenRouterClient.generate')
    @patch('core.llm.clients.GroqClient.generate')
    async def test_generate_all_fail(self, mock_groq_generate, mock_openrouter_generate,
                                   router, sample_request):
        """Test generation when all models fail"""
        # Both clients fail
        mock_openrouter_generate.side_effect = RateLimitError(
            "Rate limit exceeded",
            api_service="openrouter"
        )
        mock_groq_generate.side_effect = APIError(
            "API error",
            api_service="groq"
        )
        
        async with router:
            with pytest.raises((RateLimitError, APIError)):
                await router.generate(sample_request, fallback_on_error=True)
    
    def test_usage_summary(self, router):
        """Test usage summary generation"""
        summary = router.get_usage_summary()
        
        assert 'current_spend' in summary
        assert 'budget_config' in summary
        assert 'model_health' in summary
        assert 'routing_strategy' in summary
        assert 'client_stats' in summary
        
        assert summary['routing_strategy'] == RoutingStrategy.COST_OPTIMIZED.value
    
    def test_stats_reset(self, router):
        """Test statistics reset functionality"""
        # Set some usage
        router.current_spend['daily'] = 10.0
        router.current_spend['weekly'] = 25.0
        router.current_spend['monthly'] = 100.0
        
        # Reset daily
        router.reset_daily_stats()
        assert router.current_spend['daily'] == 0.0
        assert router.current_spend['weekly'] == 25.0
        assert router.current_spend['monthly'] == 100.0
        
        # Reset weekly
        router.reset_weekly_stats()
        assert router.current_spend['weekly'] == 0.0
        assert router.current_spend['monthly'] == 100.0
        
        # Reset monthly
        router.reset_monthly_stats()
        assert router.current_spend['monthly'] == 0.0
    
    def test_strategy_update(self, router):
        """Test routing strategy update"""
        assert router.routing_strategy == RoutingStrategy.COST_OPTIMIZED
        
        router.update_routing_strategy(RoutingStrategy.PERFORMANCE_OPTIMIZED)
        assert router.routing_strategy == RoutingStrategy.PERFORMANCE_OPTIMIZED
    
    def test_budget_update(self, router):
        """Test budget configuration update"""
        new_budget = BudgetConfig(daily_budget_usd=20.0)
        router.update_budget_config(new_budget)
        
        assert router.budget_config.daily_budget_usd == 20.0
    
    @patch('core.llm.clients.OpenRouterClient.generate')
    async def test_circuit_breaker_integration(self, mock_generate, router, sample_request):
        """Test circuit breaker integration with routing"""
        # Cause multiple failures to open circuit breaker
        mock_generate.side_effect = APIError("API error", api_service="openrouter")
        
        async with router:
            # First few requests should fail and record failures
            for _ in range(3):
                try:
                    await router.generate(sample_request, fallback_on_error=False)
                except APIError:
                    pass
            
            # Circuit breaker should now be open for the model
            model_health = router._get_model_health(DEEPSEEK_R1_CONFIG.name)
            assert not model_health['is_healthy']


class TestRoutingStrategies:
    """Test different routing strategies"""
    
    @pytest.fixture
    def sample_request(self):
        """Create sample LLM request"""
        return LLMRequest(
            prompt="Test prompt",
            model_config=DEEPSEEK_R1_CONFIG
        )
    
    def test_cost_optimized_strategy(self, sample_request):
        """Test cost-optimized routing strategy"""
        router = LLMRouter(
            openrouter_api_key="test",
            groq_api_key="test",
            routing_strategy=RoutingStrategy.COST_OPTIMIZED
        )
        
        decision = router.make_routing_decision(sample_request)
        
        # Should prefer free models
        assert decision.selected_model.cost_per_token == 0.0
        assert "free tier" in decision.routing_reason.lower()
    
    def test_performance_optimized_strategy(self, sample_request):
        """Test performance-optimized routing strategy"""
        router = LLMRouter(
            openrouter_api_key="test",
            groq_api_key="test",
            routing_strategy=RoutingStrategy.PERFORMANCE_OPTIMIZED
        )
        
        decision = router.make_routing_decision(sample_request)
        
        # Should consider performance factors
        assert decision.estimated_latency > 0
        assert decision.confidence_score > 0
    
    def test_quality_optimized_strategy(self, sample_request):
        """Test quality-optimized routing strategy"""
        router = LLMRouter(
            openrouter_api_key="test",
            groq_api_key="test",
            routing_strategy=RoutingStrategy.QUALITY_OPTIMIZED
        )
        
        decision = router.make_routing_decision(sample_request)
        
        # Should prefer reasoning models for complex tasks
        if decision.selected_model.model_type == ModelType.REASONING:
            assert decision.selected_model == DEEPSEEK_R1_CONFIG
    
    def test_balanced_strategy(self, sample_request):
        """Test balanced routing strategy"""
        router = LLMRouter(
            openrouter_api_key="test",
            groq_api_key="test",
            routing_strategy=RoutingStrategy.BALANCED
        )
        
        decision = router.make_routing_decision(sample_request)
        
        # Should balance multiple factors
        assert decision.selected_model is not None
        assert len(decision.fallback_models) > 0
        assert decision.confidence_score > 0


@pytest.mark.asyncio
async def test_router_context_manager():
    """Test router async context manager"""
    router = LLMRouter(
        openrouter_api_key="test",
        groq_api_key="test"
    )
    
    async with router as r:
        assert r is router
        # Clients should be initialized
        for client in router.clients.values():
            assert client.session is not None


if __name__ == "__main__":
    pytest.main([__file__])