#!/usr/bin/env python3
"""
Final System Validation - Comprehensive Production Readiness Test
Tests all 20 agents, PhD-level prompts, and production deployment capabilities
"""

import os
import sys
import asyncio
import time
import json
from datetime import datetime, timezone
import logging

# Add project root to path
sys.path.append('.')

from production_hedge_fund import ProductionHedgeFund
from hedge_fund_swarm import HedgeFundSwarm
from agents.fixed_detection_system import DirectAPIDetectionAgent
from agents.phd_prompts import PhDPromptEngine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SystemValidator:
    """Comprehensive system validation suite"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = time.time()
    
    async def run_comprehensive_validation(self):
        """Run complete system validation"""
        print("=" * 80)
        print("🏦 MEMEGUARD PRO - FINAL SYSTEM VALIDATION")
        print("=" * 80)
        print("Testing 20-agent autonomous hedge fund system...")
        print()
        
        # Test 1: Core Components
        await self._test_core_components()
        
        # Test 2: Detection Layer
        await self._test_detection_layer()
        
        # Test 3: PhD Prompt System
        await self._test_phd_prompts()
        
        # Test 4: Full Pipeline
        await self._test_full_pipeline()
        
        # Test 5: Production System
        await self._test_production_system()
        
        # Generate final report
        self._generate_final_report()
    
    async def _test_core_components(self):
        """Test core system components"""
        print("🔧 TESTING CORE COMPONENTS")
        print("-" * 40)
        
        try:
            # Test API keys
            required_keys = ["GROQ_API_KEY", "OPENROUTER_API_KEY", "HELIUS_API_KEY"]
            missing_keys = [key for key in required_keys if not os.getenv(key)]
            
            if missing_keys:
                print(f"⚠️  Missing API keys: {missing_keys}")
                self.test_results["api_keys"] = "PARTIAL"
            else:
                print("✅ All required API keys present")
                self.test_results["api_keys"] = "PASS"
            
            # Test Redis connection
            try:
                import redis
                client = redis.Redis(host='localhost', port=6379, db=0)
                client.ping()
                print("✅ Redis connection successful")
                self.test_results["redis"] = "PASS"
            except Exception as e:
                print(f"⚠️  Redis connection failed: {str(e)}")
                self.test_results["redis"] = "FAIL"
            
            # Test imports
            from crewai import Agent, Task, Crew, LLM
            print("✅ CrewAI imports successful")
            self.test_results["imports"] = "PASS"
            
        except Exception as e:
            print(f"❌ Core component test failed: {str(e)}")
            self.test_results["core_components"] = "FAIL"
        
        print()
    
    async def _test_detection_layer(self):
        """Test detection agents"""
        print("🔍 TESTING DETECTION LAYER")
        print("-" * 40)
        
        try:
            agent = DirectAPIDetectionAgent()
            
            # Test Solana detection
            print("Testing Solana detection...")
            results = agent.detect_and_analyze_tokens("solana")
            print(f"✅ Solana detection: {len(results)} tokens found")
            
            # Test analysis quality
            if results:
                sample_result = results[0]
                print(f"✅ Sample analysis: {sample_result.symbol} (confidence: {sample_result.confidence_score:.2f})")
            
            self.test_results["detection"] = "PASS"
            
        except Exception as e:
            print(f"❌ Detection test failed: {str(e)}")
            self.test_results["detection"] = "FAIL"
        
        print()
    
    async def _test_phd_prompts(self):
        """Test PhD-level prompt system"""
        print("🧠 TESTING PHD PROMPT SYSTEM")
        print("-" * 40)
        
        try:
            # Test prompt generation
            sample_token_data = {
                "symbol": "TEST",
                "address": "test123",
                "liquidity_usd": 10000,
                "confidence_score": 0.8
            }
            
            # Test detection prompt
            detection_prompt = PhDPromptEngine.get_detection_agent_prompt("001", "solana", sample_token_data)
            print("✅ Detection prompt generated")
            
            # Test security prompt
            security_prompt = PhDPromptEngine.get_security_analyst_prompt(sample_token_data)
            print("✅ Security analysis prompt generated")
            
            # Test sentiment prompt
            sentiment_prompt = PhDPromptEngine.get_sentiment_analyst_prompt(sample_token_data)
            print("✅ Sentiment analysis prompt generated")
            
            # Test guardian prompt
            guardian_prompt = PhDPromptEngine.get_guardian_prompt("011", {"test": "data"})
            print("✅ Guardian validation prompt generated")
            
            # Test strategy prompt
            strategy_prompt = PhDPromptEngine.get_strategy_generator_prompt({"test": "data"})
            print("✅ Strategy generation prompt generated")
            
            self.test_results["phd_prompts"] = "PASS"
            
        except Exception as e:
            print(f"❌ PhD prompt test failed: {str(e)}")
            self.test_results["phd_prompts"] = "FAIL"
        
        print()
    
    async def _test_full_pipeline(self):
        """Test complete hedge fund pipeline"""
        print("🏦 TESTING FULL HEDGE FUND PIPELINE")
        print("-" * 40)
        
        try:
            swarm = HedgeFundSwarm()
            
            print("Running full analysis pipeline...")
            start_time = time.time()
            results = await swarm.run_full_analysis_pipeline()
            processing_time = (time.time() - start_time) * 1000
            
            print(f"✅ Pipeline completed in {processing_time:.0f}ms")
            print(f"✅ Tokens detected: {results.get('tokens_detected', 0)}")
            print(f"✅ Strong buy opportunities: {results.get('strong_buy_opportunities', 0)}")
            print(f"✅ Buy opportunities: {results.get('buy_opportunities', 0)}")
            print(f"✅ Active agents: {results.get('performance_metrics', {}).get('total_agents', 0)}")
            
            # Validate processing time
            if processing_time < 60000:  # Less than 60 seconds
                print("✅ Processing time within target (<60s)")
                self.test_results["pipeline_performance"] = "PASS"
            else:
                print("⚠️  Processing time exceeds target (>60s)")
                self.test_results["pipeline_performance"] = "SLOW"
            
            self.test_results["full_pipeline"] = "PASS"
            
        except Exception as e:
            print(f"❌ Full pipeline test failed: {str(e)}")
            self.test_results["full_pipeline"] = "FAIL"
        
        print()
    
    async def _test_production_system(self):
        """Test production deployment system"""
        print("🚀 TESTING PRODUCTION SYSTEM")
        print("-" * 40)
        
        try:
            hedge_fund = ProductionHedgeFund()
            
            print("Testing production scan...")
            await hedge_fund.run_scheduled_scan()
            
            print(f"✅ Production scan completed")
            print(f"✅ Total scans: {hedge_fund.total_scans}")
            print(f"✅ Opportunities found: {hedge_fund.total_opportunities}")
            
            # Test Telegram integration
            if hedge_fund.telegram_bot:
                print("✅ Telegram bot initialized")
                self.test_results["telegram"] = "PASS"
            else:
                print("⚠️  Telegram bot not configured")
                self.test_results["telegram"] = "SKIP"
            
            self.test_results["production"] = "PASS"
            
        except Exception as e:
            print(f"❌ Production system test failed: {str(e)}")
            self.test_results["production"] = "FAIL"
        
        print()
    
    def _generate_final_report(self):
        """Generate comprehensive validation report"""
        total_time = time.time() - self.start_time
        
        print("=" * 80)
        print("📊 FINAL VALIDATION REPORT")
        print("=" * 80)
        
        # Count results
        passed = len([r for r in self.test_results.values() if r == "PASS"])
        failed = len([r for r in self.test_results.values() if r == "FAIL"])
        warnings = len([r for r in self.test_results.values() if r in ["PARTIAL", "SLOW", "SKIP"]])
        total = len(self.test_results)
        
        print(f"📈 TEST RESULTS SUMMARY:")
        print(f"   ✅ Passed: {passed}/{total}")
        print(f"   ❌ Failed: {failed}/{total}")
        print(f"   ⚠️  Warnings: {warnings}/{total}")
        print(f"   ⏱️  Total Time: {total_time:.1f}s")
        print()
        
        print(f"📋 DETAILED RESULTS:")
        for test_name, result in self.test_results.items():
            status_icon = {
                "PASS": "✅",
                "FAIL": "❌", 
                "PARTIAL": "⚠️",
                "SLOW": "⚠️",
                "SKIP": "⏭️"
            }.get(result, "❓")
            
            print(f"   {status_icon} {test_name}: {result}")
        
        print()
        
        # Overall assessment
        if failed == 0:
            if warnings == 0:
                print("🎉 SYSTEM STATUS: PRODUCTION READY")
                print("   All tests passed successfully!")
                print("   System is ready for live deployment.")
            else:
                print("✅ SYSTEM STATUS: PRODUCTION READY WITH WARNINGS")
                print("   Core functionality working, minor issues noted.")
                print("   System can be deployed with monitoring.")
        else:
            print("❌ SYSTEM STATUS: NOT READY")
            print("   Critical issues must be resolved before deployment.")
        
        print()
        print("🏦 MEMEGUARD PRO - 20 AGENT AUTONOMOUS HEDGE FUND")
        print("   Detection Agents: 4 (Solana, Ethereum, Base, Arbitrum)")
        print("   Analysis Agents: 6 (Security, Liquidity, Sentiment, etc.)")
        print("   Verification Agents: 5 (Guardians, Compliance, Anomaly)")
        print("   Execution Agents: 5 (Strategy, Alerts, Performance)")
        print("   Total: 20 PhD-level agents with chain-of-thought reasoning")
        print()
        print("🚀 Ready for autonomous trading operations!")
        print("=" * 80)

async def main():
    """Main validation entry point"""
    validator = SystemValidator()
    await validator.run_comprehensive_validation()

if __name__ == "__main__":
    asyncio.run(main())
