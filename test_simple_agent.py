#!/usr/bin/env python3
"""
Simple Agent Test - Isolate the LLM response parsing issue
"""

import sys
import os
sys.path.append('.')

def test_minimal_agent():
    """Test with the absolute minimal agent configuration"""
    print("=" * 60)
    print("TESTING MINIMAL AGENT CONFIGURATION")
    print("=" * 60)
    
    try:
        from crewai import Agent, Task, Crew, LLM
        from agents.detection_agents import get_new_pairs_dexscreener
        
        # Test tool works independently
        print("1. Testing tool independently...")
        result = get_new_pairs_dexscreener.run("solana")
        print(f"✅ Tool works: {len(result)} chars")
        print(f"Sample: {result[:100]}...")
        
        # Create minimal LLM - Try OpenAI GPT-4o-mini (known to work with CrewAI)
        print("\n2. Creating minimal LLM...")
        llm = LLM(
            model="gpt-4o-mini",
            api_key=os.getenv("OPENAI_API_KEY"),
            temperature=0.0,
            max_tokens=200
        )
        print("✅ LLM created")
        
        # Create minimal agent with FIX for Groq models
        print("\n3. Creating minimal agent...")
        agent = Agent(
            role="Tester",
            goal="Test tool usage",
            backstory="I test tools",
            tools=[get_new_pairs_dexscreener],
            llm=llm,
            verbose=False,
            max_iter=1,
            allow_delegation=False,
            use_system_prompt=False  # FIX: Groq models have issues with system prompts
        )
        print("✅ Agent created")
        
        # Create minimal task
        print("\n4. Creating minimal task...")
        task = Task(
            description="Use get_new_pairs_dexscreener with chain='solana'. Say 'Done' when finished.",
            expected_output="Simple confirmation",
            agent=agent
        )
        print("✅ Task created")
        
        # Create and run crew
        print("\n5. Running crew...")
        crew = Crew(
            agents=[agent],
            tasks=[task],
            verbose=False
        )
        
        result = crew.kickoff()
        
        print("\n" + "=" * 60)
        print("✅ SUCCESS!")
        print("=" * 60)
        print(f"Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_without_tools():
    """Test agent without any tools to isolate the issue"""
    print("\n" + "=" * 60)
    print("TESTING AGENT WITHOUT TOOLS")
    print("=" * 60)
    
    try:
        from crewai import Agent, Task, Crew, LLM
        
        # Create LLM - Try OpenAI GPT-4o-mini
        llm = LLM(
            model="gpt-4o-mini",
            api_key=os.getenv("OPENAI_API_KEY"),
            temperature=0.0,
            max_tokens=100
        )
        
        # Create agent without tools
        agent = Agent(
            role="Simple Responder",
            goal="Respond to simple questions",
            backstory="I answer questions simply",
            tools=[],  # No tools
            llm=llm,
            verbose=False,
            max_iter=1,
            allow_delegation=False,
            use_system_prompt=False  # FIX: Groq compatibility
        )
        
        # Create simple task
        task = Task(
            description="Say 'Hello World' and nothing else.",
            expected_output="Hello World",
            agent=agent
        )
        
        # Run crew
        crew = Crew(
            agents=[agent],
            tasks=[task],
            verbose=False
        )
        
        result = crew.kickoff()
        
        print(f"✅ No-tools test result: {result}")
        return True
        
    except Exception as e:
        print(f"❌ No-tools test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Starting diagnostic tests...")
    
    # Test 1: Agent without tools
    success1 = test_without_tools()
    
    # Test 2: Agent with tools (if first test passes)
    if success1:
        success2 = test_minimal_agent()
        if success2:
            print("\n🎉 ALL TESTS PASSED - AGENTS ARE WORKING!")
        else:
            print("\n⚠️  Tool integration issue identified")
    else:
        print("\n⚠️  Basic LLM response issue identified")
    
    print("\nDiagnostic complete.")
