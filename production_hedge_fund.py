#!/usr/bin/env python3
"""
MemeGuard Pro - Production Hedge Fund System
Autonomous 20-agent hedge fund with monitoring, alerting, and continuous operation
"""

import os
import sys
import asyncio
import logging
import time
import json
from datetime import datetime, timezone
from typing import Dict, List, Any
import schedule
import telegram
from dataclasses import asdict

# Add project root to path
sys.path.append('.')

from hedge_fund_swarm import HedgeFundSwarm

# Configure production logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hedge_fund.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionHedgeFund:
    """
    Production-ready autonomous hedge fund system
    Features:
    - Continuous operation with scheduled scans
    - Telegram alerts for opportunities
    - Performance tracking and analytics
    - Error handling and recovery
    - Cost monitoring and budget management
    """
    
    def __init__(self):
        self.swarm = HedgeFundSwarm()
        self.telegram_bot = self._init_telegram_bot()
        self.chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        # Performance tracking
        self.total_scans = 0
        self.total_opportunities = 0
        self.total_strong_buys = 0
        self.start_time = datetime.now(timezone.utc)
        
        # Cost tracking
        self.api_calls_count = 0
        self.estimated_cost_usd = 0.0
        
        logger.info("🏦 Production Hedge Fund System initialized")
    
    def _init_telegram_bot(self):
        """Initialize Telegram bot for alerts"""
        try:
            bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
            if bot_token:
                bot = telegram.Bot(token=bot_token)
                logger.info("✅ Telegram bot initialized")
                return bot
            else:
                logger.warning("⚠️  Telegram bot token not found")
                return None
        except Exception as e:
            logger.error(f"❌ Telegram bot initialization failed: {str(e)}")
            return None
    
    async def send_telegram_alert(self, message: str):
        """Send alert via Telegram"""
        try:
            if self.telegram_bot and self.chat_id:
                await self.telegram_bot.send_message(
                    chat_id=self.chat_id,
                    text=message,
                    parse_mode='Markdown'
                )
                logger.info("📱 Telegram alert sent")
            else:
                logger.info(f"📱 Alert (no Telegram): {message}")
        except Exception as e:
            logger.error(f"❌ Telegram alert failed: {str(e)}")
    
    async def run_scheduled_scan(self):
        """Run scheduled hedge fund analysis scan"""
        scan_start = time.time()
        self.total_scans += 1
        
        logger.info(f"🔍 Starting scheduled scan #{self.total_scans}")
        
        try:
            # Run full analysis pipeline
            results = await self.swarm.run_full_analysis_pipeline()
            
            # Update performance metrics
            self.total_opportunities += results.get('total_opportunities', 0)
            self.total_strong_buys += results.get('strong_buy_opportunities', 0)
            
            # Process results
            await self._process_scan_results(results)
            
            scan_time = time.time() - scan_start
            logger.info(f"✅ Scan #{self.total_scans} completed in {scan_time:.1f}s")
            
            # Send performance update every 10 scans
            if self.total_scans % 10 == 0:
                await self._send_performance_update()
                
        except Exception as e:
            logger.error(f"❌ Scheduled scan failed: {str(e)}")
            await self.send_telegram_alert(f"🚨 *SYSTEM ERROR*\nScan #{self.total_scans} failed: {str(e)}")
    
    async def _process_scan_results(self, results: Dict[str, Any]):
        """Process and alert on scan results"""
        try:
            tokens_detected = results.get('tokens_detected', 0)
            strong_buys = results.get('strong_buy_opportunities', 0)
            buys = results.get('buy_opportunities', 0)
            processing_time = results.get('processing_time_ms', 0)
            
            # Log scan summary
            logger.info(f"📊 Scan Results: {tokens_detected} detected, {strong_buys} strong buys, {buys} buys")
            
            # Send alerts for investment opportunities
            if strong_buys > 0:
                await self._send_strong_buy_alert(results)
            elif buys > 0:
                await self._send_buy_alert(results)
            
            # Log detailed results
            for token_data in results.get('tokens', []):
                token = token_data['token']
                final_rec = token_data.get('final_recommendation', {})
                
                logger.info(f"🪙 {token['symbol']}: {final_rec.get('recommendation', 'UNKNOWN')} "
                           f"(score: {final_rec.get('composite_score', 0.0):.2f})")
                
        except Exception as e:
            logger.error(f"❌ Results processing failed: {str(e)}")
    
    async def _send_strong_buy_alert(self, results: Dict[str, Any]):
        """Send strong buy opportunity alert"""
        try:
            strong_buy_tokens = [
                t for t in results.get('tokens', [])
                if t.get('final_recommendation', {}).get('recommendation') == 'STRONG_BUY'
            ]
            
            for token_data in strong_buy_tokens:
                token = token_data['token']
                final_rec = token_data.get('final_recommendation', {})
                phd_analysis = token_data.get('phd_analysis', {})
                
                message = f"""
🚀 *STRONG BUY OPPORTUNITY*

💎 **{token['symbol']}** ({token['address'][:8]}...)
💰 Liquidity: ${token['liquidity_usd']:,.0f}
📊 Composite Score: {final_rec.get('composite_score', 0.0):.2f}
🔒 Security: {final_rec.get('security_score', 0.0):.2f}
💭 Sentiment: {final_rec.get('sentiment_score', 0.0):.2f}
🎯 Confidence: {final_rec.get('confidence', 0.0):.2f}

⚡ *Action Required*: Consider position sizing based on risk tolerance
"""
                
                await self.send_telegram_alert(message)
                
        except Exception as e:
            logger.error(f"❌ Strong buy alert failed: {str(e)}")
    
    async def _send_buy_alert(self, results: Dict[str, Any]):
        """Send buy opportunity alert"""
        try:
            buy_tokens = [
                t for t in results.get('tokens', [])
                if t.get('final_recommendation', {}).get('recommendation') == 'BUY'
            ]
            
            if buy_tokens:
                message = f"📈 *BUY OPPORTUNITIES DETECTED*\n\n"
                
                for token_data in buy_tokens:
                    token = token_data['token']
                    final_rec = token_data.get('final_recommendation', {})
                    
                    message += f"• **{token['symbol']}**: Score {final_rec.get('composite_score', 0.0):.2f}\n"
                
                message += f"\n🔍 Total: {len(buy_tokens)} opportunities"
                await self.send_telegram_alert(message)
                
        except Exception as e:
            logger.error(f"❌ Buy alert failed: {str(e)}")
    
    async def _send_performance_update(self):
        """Send periodic performance update"""
        try:
            uptime = datetime.now(timezone.utc) - self.start_time
            uptime_hours = uptime.total_seconds() / 3600
            
            message = f"""
📊 *HEDGE FUND PERFORMANCE UPDATE*

🕐 Uptime: {uptime_hours:.1f} hours
🔍 Total Scans: {self.total_scans}
🎯 Opportunities Found: {self.total_opportunities}
🚀 Strong Buys: {self.total_strong_buys}
💰 Est. Cost: ${self.estimated_cost_usd:.2f}

⚡ System Status: OPERATIONAL
"""
            
            await self.send_telegram_alert(message)
            
        except Exception as e:
            logger.error(f"❌ Performance update failed: {str(e)}")
    
    def start_continuous_operation(self):
        """Start continuous hedge fund operation"""
        logger.info("🚀 Starting continuous hedge fund operation...")
        
        # Schedule scans every 5 minutes
        schedule.every(5).minutes.do(lambda: asyncio.create_task(self.run_scheduled_scan()))
        
        # Schedule performance updates every hour
        schedule.every().hour.do(lambda: asyncio.create_task(self._send_performance_update()))
        
        # Send startup notification
        asyncio.create_task(self.send_telegram_alert(
            "🏦 *HEDGE FUND SYSTEM STARTED*\n\n"
            "✅ 20 agents active\n"
            "🔍 Scanning every 5 minutes\n"
            "📊 Performance updates hourly"
        ))
        
        # Main operation loop
        try:
            while True:
                schedule.run_pending()
                time.sleep(30)  # Check every 30 seconds
                
        except KeyboardInterrupt:
            logger.info("🛑 Hedge fund operation stopped by user")
            asyncio.create_task(self.send_telegram_alert("🛑 *HEDGE FUND SYSTEM STOPPED*"))
        except Exception as e:
            logger.error(f"❌ Critical system error: {str(e)}")
            asyncio.create_task(self.send_telegram_alert(f"🚨 *CRITICAL ERROR*\n{str(e)}"))

# Test function
async def test_production_system():
    """Test the production hedge fund system"""
    print("=" * 80)
    print("🏦 TESTING PRODUCTION HEDGE FUND SYSTEM")
    print("=" * 80)
    
    try:
        # Initialize production system
        hedge_fund = ProductionHedgeFund()
        
        # Run single test scan
        print("🔍 Running test scan...")
        await hedge_fund.run_scheduled_scan()
        
        # Display performance metrics
        print(f"\n📊 SYSTEM METRICS:")
        print(f"Total Scans: {hedge_fund.total_scans}")
        print(f"Total Opportunities: {hedge_fund.total_opportunities}")
        print(f"Strong Buys Found: {hedge_fund.total_strong_buys}")
        
        print(f"\n✅ Production system test completed successfully!")
        print(f"🚀 Ready for continuous operation!")
        
        return True
        
    except Exception as e:
        print(f"❌ Production test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="MemeGuard Pro - Autonomous Hedge Fund")
    parser.add_argument("--test", action="store_true", help="Run test mode")
    parser.add_argument("--continuous", action="store_true", help="Start continuous operation")
    
    args = parser.parse_args()
    
    if args.test:
        asyncio.run(test_production_system())
    elif args.continuous:
        hedge_fund = ProductionHedgeFund()
        hedge_fund.start_continuous_operation()
    else:
        print("Usage: python production_hedge_fund.py --test | --continuous")

if __name__ == "__main__":
    main()
