"""
MemeGuard Pro - Logging Infrastructure
Structured logging with multiple outputs and correlation tracking
"""

import os
import sys
import json
import logging
import logging.handlers
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path
import structlog
from structlog.stdlib import LoggerFactory
import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Add extra fields if present
        if hasattr(record, 'correlation_id'):
            log_entry['correlation_id'] = record.correlation_id
        
        if hasattr(record, 'agent_id'):
            log_entry['agent_id'] = record.agent_id
            
        if hasattr(record, 'token_address'):
            log_entry['token_address'] = record.token_address
            
        if hasattr(record, 'processing_time'):
            log_entry['processing_time'] = record.processing_time
            
        if hasattr(record, 'api_cost'):
            log_entry['api_cost'] = record.api_cost
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add stack trace if present
        if record.stack_info:
            log_entry['stack_trace'] = record.stack_info
            
        return json.dumps(log_entry, default=str)


class CorrelationFilter(logging.Filter):
    """Filter to add correlation ID to log records"""
    
    def __init__(self, correlation_id: Optional[str] = None):
        super().__init__()
        self.correlation_id = correlation_id
    
    def filter(self, record: logging.LogRecord) -> bool:
        if self.correlation_id:
            record.correlation_id = self.correlation_id
        return True


class AgentFilter(logging.Filter):
    """Filter to add agent ID to log records"""
    
    def __init__(self, agent_id: Optional[str] = None):
        super().__init__()
        self.agent_id = agent_id
    
    def filter(self, record: logging.LogRecord) -> bool:
        if self.agent_id:
            record.agent_id = self.agent_id
        return True


def setup_sentry(dsn: Optional[str], environment: str, debug: bool = False) -> None:
    """
    Setup Sentry error tracking
    
    Args:
        dsn: Sentry DSN
        environment: Application environment
        debug: Enable debug mode
    """
    if not dsn:
        return
    
    sentry_logging = LoggingIntegration(
        level=logging.INFO,        # Capture info and above as breadcrumbs
        event_level=logging.ERROR  # Send errors as events
    )
    
    sentry_sdk.init(
        dsn=dsn,
        integrations=[sentry_logging],
        environment=environment,
        debug=debug,
        traces_sample_rate=0.1 if environment == 'production' else 1.0,
        profiles_sample_rate=0.1 if environment == 'production' else 1.0,
        attach_stacktrace=True,
        send_default_pii=False,  # Don't send PII for compliance
    )


def setup_logging(
    log_level: str = "INFO",
    environment: str = "development",
    sentry_dsn: Optional[str] = None,
    debug: bool = False,
    log_dir: Optional[str] = None,
) -> None:
    """
    Setup comprehensive logging infrastructure
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        environment: Application environment
        sentry_dsn: Sentry DSN for error tracking
        debug: Enable debug mode
        log_dir: Directory for log files
    """
    
    # Create log directory if specified
    if log_dir:
        Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Get root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler with colored output for development
    console_handler = logging.StreamHandler(sys.stdout)
    
    if environment == 'development' and not debug:
        # Use simple format for development
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    else:
        # Use JSON format for production and debug
        console_formatter = JSONFormatter()
    
    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(getattr(logging, log_level.upper()))
    root_logger.addHandler(console_handler)
    
    # File handlers for persistent logging
    if log_dir:
        # General application log
        app_log_file = os.path.join(log_dir, 'memeguard.log')
        app_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=50 * 1024 * 1024,  # 50MB
            backupCount=10
        )
        app_handler.setFormatter(JSONFormatter())
        app_handler.setLevel(logging.INFO)
        root_logger.addHandler(app_handler)
        
        # Error log
        error_log_file = os.path.join(log_dir, 'errors.log')
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5
        )
        error_handler.setFormatter(JSONFormatter())
        error_handler.setLevel(logging.ERROR)
        root_logger.addHandler(error_handler)
        
        # Agent activity log
        agent_log_file = os.path.join(log_dir, 'agents.log')
        agent_handler = logging.handlers.RotatingFileHandler(
            agent_log_file,
            maxBytes=100 * 1024 * 1024,  # 100MB
            backupCount=20
        )
        agent_handler.setFormatter(JSONFormatter())
        agent_handler.setLevel(logging.DEBUG)
        
        # Only log agent-related messages to agent log
        agent_filter = logging.Filter()
        agent_filter.filter = lambda record: 'agent' in record.name.lower()
        agent_handler.addFilter(agent_filter)
        root_logger.addHandler(agent_handler)
        
        # Performance metrics log
        metrics_log_file = os.path.join(log_dir, 'metrics.log')
        metrics_handler = logging.handlers.RotatingFileHandler(
            metrics_log_file,
            maxBytes=50 * 1024 * 1024,  # 50MB
            backupCount=10
        )
        metrics_handler.setFormatter(JSONFormatter())
        metrics_handler.setLevel(logging.INFO)
        
        # Only log metrics-related messages
        metrics_filter = logging.Filter()
        metrics_filter.filter = lambda record: any(
            keyword in record.getMessage().lower() 
            for keyword in ['metric', 'performance', 'latency', 'cost']
        )
        metrics_handler.addFilter(metrics_filter)
        root_logger.addHandler(metrics_handler)
    
    # Setup Sentry error tracking
    if sentry_dsn:
        setup_sentry(sentry_dsn, environment, debug)
    
    # Configure specific loggers
    configure_third_party_loggers(log_level)
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info(
        "Logging infrastructure initialized",
        extra={
            'log_level': log_level,
            'environment': environment,
            'sentry_enabled': bool(sentry_dsn),
            'log_dir': log_dir,
        }
    )


def get_logger(name: str) -> logging.Logger:
    """
    Get a simple logger instance with the specified name
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)


def get_agent_logger(agent_id: str, correlation_id: Optional[str] = None) -> logging.Logger:
    """
    Get a logger instance configured for a specific agent
    
    Args:
        agent_id: Unique agent identifier
        correlation_id: Optional correlation ID for request tracking
        
    Returns:
        Logger with agent-specific filters
    """
    logger = logging.getLogger(f"agent.{agent_id}")
    
    # Add agent filter
    agent_filter = AgentFilter(agent_id)
    logger.addFilter(agent_filter)
    
    # Add correlation filter if provided
    if correlation_id:
        correlation_filter = CorrelationFilter(correlation_id)
        logger.addFilter(correlation_filter)
    
    return logger


def create_correlation_id() -> str:
    """Create a unique correlation ID for request tracking"""
    import uuid
    return str(uuid.uuid4())


class LogContext:
    """Context manager for adding structured logging context"""
    
    def __init__(self, logger: logging.Logger, **context):
        self.logger = logger
        self.context = context
        self.old_filters = []
    
    def __enter__(self):
        # Add context filters
        for key, value in self.context.items():
            filter_func = lambda record, k=key, v=value: setattr(record, k, v) or True
            self.logger.addFilter(filter_func)
            self.old_filters.append(filter_func)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Remove context filters
        for filter_func in self.old_filters:
            self.logger.removeFilter(filter_func)


def configure_third_party_loggers(log_level: str) -> None:
    """Configure third-party library loggers"""
    
    # Reduce noise from third-party libraries
    noisy_loggers = [
        'urllib3.connectionpool',
        'requests.packages.urllib3',
        'asyncio',
        'aiohttp.access',
        'websockets.protocol',
        'redis.connection',
        'sqlalchemy.engine',
        'crewai.agent',
    ]
    
    for logger_name in noisy_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.WARNING)
    
    # Set specific levels for important loggers
    important_loggers = {
        'agents': log_level,
        'core': log_level,
        'memeguard': log_level,
        'crewai': 'INFO',
        'langchain': 'WARNING',
        'openai': 'WARNING',
    }
    
    for logger_name, level in important_loggers.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(getattr(logging, level.upper()))


def get_logger(
    name: str,
    correlation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    **kwargs
) -> logging.Logger:
    """
    Get a logger with optional correlation ID and agent ID
    
    Args:
        name: Logger name
        correlation_id: Correlation ID for request tracking
        agent_id: Agent ID for agent-specific logging
        **kwargs: Additional context to add to log records
    
    Returns:
        logging.Logger: Configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Add correlation filter if provided
    if correlation_id:
        correlation_filter = CorrelationFilter(correlation_id)
        logger.addFilter(correlation_filter)
    
    # Add agent filter if provided
    if agent_id:
        agent_filter = AgentFilter(agent_id)
        logger.addFilter(agent_filter)
    
    # Add any additional context as filters
    for key, value in kwargs.items():
        filter_class = type(f'{key.title()}Filter', (logging.Filter,), {
            '__init__': lambda self, val=value: setattr(self, key, val),
            'filter': lambda self, record: setattr(record, key, getattr(self, key)) or True
        })
        logger.addFilter(filter_class())
    
    return logger


def log_performance_metric(
    logger: logging.Logger,
    metric_name: str,
    value: float,
    unit: str = "seconds",
    **context
) -> None:
    """
    Log a performance metric
    
    Args:
        logger: Logger instance
        metric_name: Name of the metric
        value: Metric value
        unit: Unit of measurement
        **context: Additional context
    """
    logger.info(
        f"Performance metric: {metric_name}",
        extra={
            'metric_name': metric_name,
            'metric_value': value,
            'metric_unit': unit,
            'processing_time': value if unit == 'seconds' else None,
            **context
        }
    )


def log_api_cost(
    logger: logging.Logger,
    service: str,
    cost: float,
    currency: str = "USD",
    **context
) -> None:
    """
    Log API cost information
    
    Args:
        logger: Logger instance
        service: API service name
        cost: Cost amount
        currency: Currency code
        **context: Additional context
    """
    logger.info(
        f"API cost: {service}",
        extra={
            'api_service': service,
            'api_cost': cost,
            'currency': currency,
            **context
        }
    )


def log_agent_activity(
    logger: logging.Logger,
    agent_id: str,
    activity: str,
    status: str,
    **context
) -> None:
    """
    Log agent activity
    
    Args:
        logger: Logger instance
        agent_id: Agent identifier
        activity: Activity description
        status: Activity status (started, completed, failed)
        **context: Additional context
    """
    logger.info(
        f"Agent {agent_id}: {activity} - {status}",
        extra={
            'agent_id': agent_id,
            'activity': activity,
            'status': status,
            **context
        }
    )


class LoggerMixin:
    """Mixin class to add logging capabilities to other classes"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._logger = None
        self._correlation_id = kwargs.get('correlation_id')
        self._agent_id = kwargs.get('agent_id')
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger instance for this class"""
        if self._logger is None:
            self._logger = get_logger(
                self.__class__.__module__ + '.' + self.__class__.__name__,
                correlation_id=self._correlation_id,
                agent_id=self._agent_id
            )
        return self._logger
    
    def log_performance(self, metric_name: str, value: float, **context) -> None:
        """Log performance metric"""
        log_performance_metric(self.logger, metric_name, value, **context)
    
    def log_cost(self, service: str, cost: float, **context) -> None:
        """Log API cost"""
        log_api_cost(self.logger, service, cost, **context)
    
    def log_activity(self, activity: str, status: str, **context) -> None:
        """Log activity"""
        if hasattr(self, 'agent_id'):
            log_agent_activity(self.logger, self.agent_id, activity, status, **context)
        else:
            self.logger.info(f"{activity} - {status}", extra=context)