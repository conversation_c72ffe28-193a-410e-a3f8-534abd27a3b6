{"summary": {"total_tests": 11, "passed_tests": 7, "failed_tests": 4, "warnings": 1, "performance_issues": 0}, "results": {"settings_loading": {"success": false, "details": "Settings error: No module named 'core.config'", "duration": 0, "timestamp": "2025-07-19T14:33:35.979980", "warning": false}, "environment_configuration": {"success": false, "details": "All 5 required env vars present", "duration": 0.23653912544250488, "timestamp": "2025-07-19T14:33:35.980100", "warning": false}, "redis_connectivity": {"success": true, "details": "Redis ping successful; Redis read/write operations successful; Redis performance good: 0.026s for 100 ops", "duration": 0.06221890449523926, "timestamp": "2025-07-19T14:33:36.042420", "warning": false}, "api_DEXScreener_Search": {"success": true, "details": "Status 200 OK; Response time: 0.109s; JSON parsed successfully; Key 'pairs' present; Found 30 pairs", "duration": 0.10993003845214844, "timestamp": "2025-07-19T14:33:36.153815", "warning": false}, "api_DEXScreener_Token_Lookup": {"success": true, "details": "Status 200 OK; Response time: 0.033s; JSON parsed successfully; Key 'pairs' present; Found 30 pairs", "duration": 0.03443002700805664, "timestamp": "2025-07-19T14:33:36.188423", "warning": false}, "api_Birdeye_Health_Check": {"success": false, "details": "Status 200 OK; Response time: 0.329s; Request failed: 200, message='Attempt to decode JSON with unexpected mimetype: text/html; charset=utf-8', url='https://docs.birdeye.so'", "duration": 0.33423590660095215, "timestamp": "2025-07-19T14:33:36.522838", "warning": true}, "api_endpoints_comprehensive": {"success": true, "details": "Tested 3 endpoints", "duration": 0.48122406005859375, "timestamp": "2025-07-19T14:33:36.523834", "warning": false}, "detection_functions": {"success": false, "details": "DEXScreener returned non-list: <class 'str'>; Comprehensive returned non-list: <class 'str'>", "duration": 7.021024942398071, "timestamp": "2025-07-19T14:33:43.545136", "warning": false}, "agent_initialization": {"success": true, "details": "AgentSystemManager created; Coordinator initialized in 0.017s; Created 1 coordinators: ['detection']; Detection coordinator available; Coordinator <PERSON><PERSON> connection OK; Coordinator shutdown successful", "duration": 0.017352819442749023, "timestamp": "2025-07-19T14:33:43.562756", "warning": false}, "system_resources": {"success": true, "details": "CPU usage: 27.6%; Memory usage: 79.9%; Disk usage: 1.6%; Network connectivity OK; Related processes: Redis (PID: 7722)", "duration": 1.0586049556732178, "timestamp": "2025-07-19T14:33:44.621486", "warning": false}, "error_handling": {"success": true, "details": "Timeout handling works correctly; API key test error: Session is closed; Redis connection failure properly handled", "duration": 0.0075418949127197266, "timestamp": "2025-07-19T14:33:44.629260", "warning": false}}, "timestamp": "2025-07-19T14:33:44.630369", "duration": 8.887202978134155}