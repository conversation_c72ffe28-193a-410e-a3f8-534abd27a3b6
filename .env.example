# MemeGuard Pro v4.0 - Environment Configuration
# Copy this file to .env and fill in your API keys

# ============================================================================
# REQUIRED API KEYS - System will not start without these
# ============================================================================

# OpenRouter API Key (for DeepSeek R1 model access)
# Get free key at: https://openrouter.ai/
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Helius API Key (for Solana RPC and enhanced APIs)  
# Get free tier at: https://helius.dev/
API_HELIUS_API_KEY=your_helius_api_key_here

# Infura API Key (for Ethereum and Base chain access)
# Get free tier at: https://infura.io/
API_INFURA_API_KEY=your_infura_api_key_here

# ============================================================================
# OPTIONAL API KEYS - Enhance functionality when provided
# ============================================================================

# Moralis API Key (for EVM chain data)
# Get free tier at: https://moralis.io/
API_MORALIS_API_KEY=your_moralis_api_key_here

# Birdeye API Key (for Solana token analytics)
# Get free tier at: https://birdeye.so/
API_BIRDEYE_API_KEY=your_birdeye_api_key_here

# Apify API Key (for Twitter/social media scraping)
# Get free tier at: https://apify.com/
API_APIFY_API_KEY=your_apify_api_key_here

# Etherscan API Key (for Ethereum transaction data)
# Get free key at: https://etherscan.io/apis
API_ETHERSCAN_API_KEY=your_etherscan_api_key_here

# TokenSniffer API Key (for additional security analysis)
# Contact TokenSniffer for API access
API_TOKENSNIFFER_KEY=your_tokensniffer_api_key_here

# ============================================================================
# NOTIFICATION SETTINGS
# ============================================================================

# Discord Webhook URL (for Discord alerts)
# Create webhook in Discord channel settings
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook_url

# Email Alert Settings (SMTP configuration)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_specific_password
ALERT_EMAIL_RECIPIENT=<EMAIL>

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================

# Redis Configuration (for caching and queues)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# PostgreSQL Configuration (for persistent data)
DATABASE_URL=postgresql://user:password@localhost:5432/memeguard
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# TimescaleDB Extension (for time-series data)
TIMESCALEDB_ENABLED=true

# ============================================================================
# SYSTEM CONFIGURATION
# ============================================================================

# Detection Settings
DETECTION_INTERVAL_SECONDS=30
MAX_TOKENS_PER_CYCLE=50

# Analysis Settings
ANALYSIS_TIMEOUT_SECONDS=300
MAX_CONCURRENT_ANALYSES=10

# Consensus Settings
CONSENSUS_TIMEOUT_SECONDS=60
MIN_CONSENSUS_VOTES=3

# Risk Management
MAX_POSITION_SIZE=0.05
MAX_TOTAL_MEME_ALLOCATION=0.20
DEFAULT_STOP_LOSS=-0.50

# ============================================================================
# LOGGING & MONITORING
# ============================================================================

# Log Level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Sentry DSN (for error tracking)
SENTRY_DSN=your_sentry_dsn_here

# Prometheus Metrics
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=8000

# ============================================================================
# DEVELOPMENT SETTINGS
# ============================================================================

# Environment Type
ENVIRONMENT=development

# Debug Mode
DEBUG=false

# Test Mode (uses mock data)
TEST_MODE=false

# Rate Limiting (requests per minute)
API_RATE_LIMIT=1000

# ============================================================================
# ADVANCED CONFIGURATION
# ============================================================================

# ChromaDB Settings (for vector storage)
CHROMADB_PERSIST_DIRECTORY=./chroma_db
CHROMADB_COLLECTION_NAME=token_analysis

# Agent Behavior Tuning
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=3000
LLM_TIMEOUT_SECONDS=120

# Portfolio Settings
PORTFOLIO_REBALANCE_FREQUENCY=3600
PORTFOLIO_RISK_SCORE_THRESHOLD=0.7

# Alert Filtering
MIN_CONFIDENCE_THRESHOLD=0.7
ALERT_COOLDOWN_MINUTES=60

# ============================================================================
# SECURITY SETTINGS
# ============================================================================

# API Key Encryption
API_KEY_ENCRYPTION_KEY=your_32_byte_encryption_key_here

# JWT Settings (if using web interface)
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440

# CORS Settings (if running web API)
CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]

# ============================================================================
# BACKUP SETTINGS
# ============================================================================

# Database Backup
DB_BACKUP_ENABLED=true
DB_BACKUP_INTERVAL_HOURS=24
DB_BACKUP_RETENTION_DAYS=30

# Configuration Backup
CONFIG_BACKUP_ENABLED=true
CONFIG_BACKUP_S3_BUCKET=your_s3_bucket_name

# ============================================================================
# NOTES
# ============================================================================

# 1. Keep your API keys secure and never commit them to version control
# 2. Use strong, unique passwords for all database connections
# 3. Enable 2FA on all API provider accounts
# 4. Regularly rotate API keys and passwords
# 5. Monitor API usage to detect unauthorized access
# 6. Use environment-specific configurations for dev/staging/production
