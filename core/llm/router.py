"""
MemeGuard Pro - LLM Router
Intelligent routing with fallback mechanisms and usage optimization
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging
from enum import Enum

from ..exceptions import APIError, RateLimitError, TimeoutError, handle_exception
from ..logging import LoggerMixin, log_performance_metric, log_api_cost
from .models import (
    LLMRequest, LLMResponse, ModelConfig, ModelType, ModelProvider,
    get_model_config, get_models_by_type, UsageStats
)
from .clients import OpenRouterClient, GroqClient, BaseLLMClient


class RoutingStrategy(Enum):
    """LLM routing strategies"""
    COST_OPTIMIZED = "cost_optimized"  # Prefer free/cheap models
    PERFORMANCE_OPTIMIZED = "performance_optimized"  # Prefer fast models
    QUALITY_OPTIMIZED = "quality_optimized"  # Prefer high-quality models
    BALANCED = "balanced"  # Balance cost, performance, and quality


@dataclass
class RoutingDecision:
    """Result of routing decision"""
    
    selected_model: ModelConfig
    fallback_models: List[ModelConfig]
    routing_reason: str
    estimated_cost: float
    estimated_latency: float
    confidence_score: float = 1.0


@dataclass
class BudgetConfig:
    """Budget configuration for cost control"""
    
    daily_budget_usd: float = 10.0
    weekly_budget_usd: float = 50.0
    monthly_budget_usd: float = 200.0
    
    # Emergency thresholds
    daily_emergency_threshold: float = 8.0
    weekly_emergency_threshold: float = 40.0
    
    # Free tier limits
    openrouter_daily_requests: int = 50
    groq_requests_per_minute: int = 30
    
    def is_budget_exceeded(self, current_spend: Dict[str, float]) -> bool:
        """Check if any budget limits are exceeded"""
        return (
            current_spend.get('daily', 0) >= self.daily_budget_usd or
            current_spend.get('weekly', 0) >= self.weekly_budget_usd or
            current_spend.get('monthly', 0) >= self.monthly_budget_usd
        )
    
    def is_emergency_threshold_reached(self, current_spend: Dict[str, float]) -> bool:
        """Check if emergency thresholds are reached"""
        return (
            current_spend.get('daily', 0) >= self.daily_emergency_threshold or
            current_spend.get('weekly', 0) >= self.weekly_emergency_threshold
        )


class LLMRouter(LoggerMixin):
    """Intelligent LLM router with fallback mechanisms and cost optimization"""
    
    def __init__(
        self,
        openrouter_api_key: str,
        groq_api_key: str,
        routing_strategy: RoutingStrategy = RoutingStrategy.COST_OPTIMIZED,
        budget_config: Optional[BudgetConfig] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        
        self.routing_strategy = routing_strategy
        self.budget_config = budget_config or BudgetConfig()
        
        # Initialize clients
        self.clients: Dict[ModelProvider, BaseLLMClient] = {}
        self._initialize_clients(openrouter_api_key, groq_api_key)
        
        # Usage tracking
        self.usage_stats: Dict[str, UsageStats] = {}
        self.current_spend = {
            'daily': 0.0,
            'weekly': 0.0,
            'monthly': 0.0
        }
        
        # Performance tracking
        self.model_performance: Dict[str, Dict[str, float]] = {}
        
        # Circuit breaker for models
        self.model_health: Dict[str, Dict[str, Any]] = {}
        
        self.logger.info(
            "LLM Router initialized",
            extra={
                'routing_strategy': routing_strategy.value,
                'budget_config': self.budget_config.__dict__,
                'available_clients': list(self.clients.keys())
            }
        )
    
    def _initialize_clients(self, openrouter_api_key: str, groq_api_key: str) -> None:
        """Initialize LLM clients"""
        try:
            # Initialize OpenRouter client
            self.clients[ModelProvider.OPENROUTER] = OpenRouterClient(
                api_key=openrouter_api_key,
                correlation_id=getattr(self, '_correlation_id', None)
            )
            
            # Initialize Groq client
            self.clients[ModelProvider.GROQ] = GroqClient(
                api_key=groq_api_key,
                correlation_id=getattr(self, '_correlation_id', None)
            )
            
            self.logger.info("LLM clients initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize LLM clients: {e}")
            raise
    
    async def __aenter__(self):
        """Async context manager entry"""
        for client in self.clients.values():
            await client.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        for client in self.clients.values():
            await client.__aexit__(exc_type, exc_val, exc_tb)
    
    def _get_model_health(self, model_name: str) -> Dict[str, Any]:
        """Get or create model health tracking"""
        if model_name not in self.model_health:
            self.model_health[model_name] = {
                'failure_count': 0,
                'last_failure': None,
                'success_count': 0,
                'last_success': None,
                'avg_response_time': 0.0,
                'is_healthy': True
            }
        return self.model_health[model_name]
    
    def _update_model_health(self, model_name: str, success: bool, response_time: float = 0.0) -> None:
        """Update model health metrics"""
        health = self._get_model_health(model_name)
        
        if success:
            health['success_count'] += 1
            health['last_success'] = datetime.utcnow()
            health['failure_count'] = max(0, health['failure_count'] - 1)  # Decay failures
            
            # Update average response time
            if health['avg_response_time'] == 0.0:
                health['avg_response_time'] = response_time
            else:
                health['avg_response_time'] = (0.9 * health['avg_response_time'] + 0.1 * response_time)
        else:
            health['failure_count'] += 1
            health['last_failure'] = datetime.utcnow()
        
        # Update health status
        health['is_healthy'] = health['failure_count'] < 3
    
    def _is_model_available(self, config: ModelConfig) -> bool:
        """Check if model is available and healthy"""
        health = self._get_model_health(config.name)
        
        # Check circuit breaker
        if not health['is_healthy']:
            # Allow retry after 5 minutes
            if health['last_failure']:
                time_since_failure = datetime.utcnow() - health['last_failure']
                if time_since_failure < timedelta(minutes=5):
                    return False
        
        # Check budget constraints
        if self.budget_config.is_budget_exceeded(self.current_spend):
            # Only allow free models when budget is exceeded
            if config.cost_per_token > 0:
                return False
        
        return True
    
    def _calculate_routing_score(self, config: ModelConfig, request: LLMRequest) -> float:
        """Calculate routing score for a model based on strategy"""
        score = 0.0
        
        # Base score from model priority
        score += (10 - config.priority) * 10
        
        # Health penalty
        health = self._get_model_health(config.name)
        if not health['is_healthy']:
            score -= 50
        
        # Performance bonus
        if health['avg_response_time'] > 0:
            # Prefer faster models (lower response time = higher score)
            score += max(0, 30 - health['avg_response_time'])
        
        # Strategy-specific scoring
        if self.routing_strategy == RoutingStrategy.COST_OPTIMIZED:
            # Prefer free models
            if config.cost_per_token == 0:
                score += 100
            else:
                score -= config.cost_per_token * 1000000  # Penalty for cost
        
        elif self.routing_strategy == RoutingStrategy.PERFORMANCE_OPTIMIZED:
            # Prefer models with better performance
            if config.provider == ModelProvider.GROQ:
                score += 20  # Groq is generally faster
            
        elif self.routing_strategy == RoutingStrategy.QUALITY_OPTIMIZED:
            # Prefer reasoning models for complex tasks
            if config.model_type == ModelType.REASONING:
                score += 30
        
        elif self.routing_strategy == RoutingStrategy.BALANCED:
            # Balance all factors
            if config.cost_per_token == 0:
                score += 50  # Free is good
            if config.model_type == ModelType.REASONING:
                score += 20  # Quality matters
            if config.provider == ModelProvider.GROQ:
                score += 10  # Speed matters
        
        return score
    
    def _select_models(self, model_type: ModelType, request: LLMRequest) -> List[ModelConfig]:
        """Select and rank models for the request"""
        # Get all models of the requested type
        candidate_models = get_models_by_type(model_type)
        
        # Filter available models
        available_models = [
            config for config in candidate_models 
            if self._is_model_available(config)
        ]
        
        if not available_models:
            # If no models available, try all models (emergency fallback)
            available_models = candidate_models
            self.logger.warning(
                "No models available with current constraints, using emergency fallback",
                extra={'model_type': model_type.value}
            )
        
        # Score and sort models
        scored_models = [
            (config, self._calculate_routing_score(config, request))
            for config in available_models
        ]
        
        # Sort by score (descending)
        scored_models.sort(key=lambda x: x[1], reverse=True)
        
        return [config for config, score in scored_models]
    
    def make_routing_decision(self, request: LLMRequest) -> RoutingDecision:
        """Make intelligent routing decision"""
        model_type = request.model_config.model_type
        
        # Select and rank models
        ranked_models = self._select_models(model_type, request)
        
        if not ranked_models:
            raise APIError(
                f"No available models for type: {model_type.value}",
                api_service="llm_router"
            )
        
        selected_model = ranked_models[0]
        fallback_models = ranked_models[1:3]  # Top 2 fallbacks
        
        # Calculate estimates
        estimated_cost = request.estimate_cost()
        health = self._get_model_health(selected_model.name)
        estimated_latency = health.get('avg_response_time', selected_model.timeout_seconds / 2)
        
        # Determine routing reason
        routing_reason = f"Selected {selected_model.name} based on {self.routing_strategy.value} strategy"
        if selected_model.cost_per_token == 0:
            routing_reason += " (free tier)"
        
        decision = RoutingDecision(
            selected_model=selected_model,
            fallback_models=fallback_models,
            routing_reason=routing_reason,
            estimated_cost=estimated_cost,
            estimated_latency=estimated_latency,
            confidence_score=0.9 if health['is_healthy'] else 0.6
        )
        
        self.logger.info(
            "Routing decision made",
            extra={
                'selected_model': selected_model.name,
                'fallback_count': len(fallback_models),
                'estimated_cost': estimated_cost,
                'estimated_latency': estimated_latency,
                'routing_reason': routing_reason
            }
        )
        
        return decision
    
    async def generate(
        self,
        request: LLMRequest,
        max_retries: int = 2,
        fallback_on_error: bool = True
    ) -> LLMResponse:
        """Generate response with intelligent routing and fallbacks"""
        start_time = time.time()
        
        # Make routing decision
        decision = self.make_routing_decision(request)
        
        # Try primary model
        models_to_try = [decision.selected_model] + decision.fallback_models
        last_error = None
        
        for attempt, model_config in enumerate(models_to_try):
            if attempt > max_retries:
                break
            
            try:
                # Update request with selected model
                request.model_config = model_config
                
                # Get appropriate client
                client = self.clients[model_config.provider]
                
                self.logger.info(
                    f"Attempting LLM request with {model_config.name} (attempt {attempt + 1})",
                    extra={
                        'model': model_config.name,
                        'provider': model_config.provider.value,
                        'attempt': attempt + 1,
                        'correlation_id': request.correlation_id
                    }
                )
                
                # Make request
                response = await client.generate(request)
                
                # Update model health
                self._update_model_health(
                    model_config.name, 
                    success=True, 
                    response_time=response.response_time_seconds
                )
                
                # Update spend tracking
                self.current_spend['daily'] += response.cost_usd
                self.current_spend['weekly'] += response.cost_usd
                self.current_spend['monthly'] += response.cost_usd
                
                # Log successful routing
                total_time = time.time() - start_time
                log_performance_metric(
                    self.logger,
                    "llm_routing_success",
                    total_time,
                    model=model_config.name,
                    attempt=attempt + 1,
                    cost=response.cost_usd
                )
                
                self.logger.info(
                    "LLM request completed successfully",
                    extra={
                        'model': model_config.name,
                        'total_time': total_time,
                        'attempts': attempt + 1,
                        'cost_usd': response.cost_usd,
                        'tokens': response.total_tokens
                    }
                )
                
                return response
                
            except (RateLimitError, TimeoutError) as e:
                # These errors should trigger fallback
                last_error = e
                self._update_model_health(model_config.name, success=False)
                
                self.logger.warning(
                    f"LLM request failed with {model_config.name}: {e}",
                    extra={
                        'model': model_config.name,
                        'error_type': type(e).__name__,
                        'attempt': attempt + 1,
                        'will_retry': attempt < len(models_to_try) - 1
                    }
                )
                
                if not fallback_on_error or attempt >= len(models_to_try) - 1:
                    break
                
                # Wait before retry
                await asyncio.sleep(min(2 ** attempt, 10))
                
            except Exception as e:
                # Other errors might not warrant fallback
                last_error = e
                self._update_model_health(model_config.name, success=False)
                
                self.logger.error(
                    f"LLM request failed with {model_config.name}: {e}",
                    extra={
                        'model': model_config.name,
                        'error_type': type(e).__name__,
                        'attempt': attempt + 1,
                    }
                )
                
                # Don't retry on certain errors
                if isinstance(e, (APIError,)) and not fallback_on_error:
                    break
        
        # All attempts failed
        total_time = time.time() - start_time
        self.logger.error(
            "All LLM routing attempts failed",
            extra={
                'models_tried': [m.name for m in models_to_try[:max_retries + 1]],
                'total_time': total_time,
                'last_error': str(last_error)
            }
        )
        
        if last_error:
            raise last_error
        else:
            raise APIError(
                "All LLM routing attempts failed",
                api_service="llm_router",
                details={'models_tried': [m.name for m in models_to_try]}
            )
    
    def get_usage_summary(self) -> Dict[str, Any]:
        """Get comprehensive usage summary"""
        summary = {
            'current_spend': self.current_spend.copy(),
            'budget_config': self.budget_config.__dict__,
            'model_health': self.model_health.copy(),
            'routing_strategy': self.routing_strategy.value,
        }
        
        # Add client usage stats
        client_stats = {}
        for provider, client in self.clients.items():
            client_stats[provider.value] = client.get_usage_summary()
        
        summary['client_stats'] = client_stats
        
        return summary
    
    def reset_daily_stats(self) -> None:
        """Reset daily statistics (call this daily)"""
        self.current_spend['daily'] = 0.0
        
        # Reset daily counters in clients
        for client in self.clients.values():
            for stats in client.usage_stats.values():
                stats.reset_day()
        
        self.logger.info("Daily statistics reset")
    
    def reset_weekly_stats(self) -> None:
        """Reset weekly statistics (call this weekly)"""
        self.current_spend['weekly'] = 0.0
        self.logger.info("Weekly statistics reset")
    
    def reset_monthly_stats(self) -> None:
        """Reset monthly statistics (call this monthly)"""
        self.current_spend['monthly'] = 0.0
        self.logger.info("Monthly statistics reset")
    
    def update_routing_strategy(self, strategy: RoutingStrategy) -> None:
        """Update routing strategy"""
        old_strategy = self.routing_strategy
        self.routing_strategy = strategy
        
        self.logger.info(
            "Routing strategy updated",
            extra={
                'old_strategy': old_strategy.value,
                'new_strategy': strategy.value
            }
        )
    
    def update_budget_config(self, budget_config: BudgetConfig) -> None:
        """Update budget configuration"""
        self.budget_config = budget_config
        
        self.logger.info(
            "Budget configuration updated",
            extra={'budget_config': budget_config.__dict__}
        )