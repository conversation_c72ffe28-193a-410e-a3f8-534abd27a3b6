# Technology Stack

## Core Framework
- **Primary Framework**: CrewAI v2.0+ (Multi-Agent Orchestration)
- **LLM Provider**: OpenRouter (DeepSeek R1-0528:free)
- **Web Framework**: FastAPI v0.104+ (Async/Await)
- **Language**: Python 3.9+

## Infrastructure & Data
- **Task Queue**: Celery + Redis
- **Message Broker**: Redis v7.2+
- **Database**: PostgreSQL v16 + TimescaleDB (time-series optimization)
- **Vector Database**: ChromaDB v0.4.15
- **Caching**: Redis + In-Memory LRU

## External APIs & Services
- **Blockchain Data**: <PERSON><PERSON> (Solana WebSocket + RPC), Infura (Ethereum/Base), DEXScreener API v1.2, Birdeye API v1.0
- **Security Analysis**: GoPlus Security API, custom honeypot detection
- **Social Intelligence**: Apify Twitter/X Scrapers, Reddit API via PRAW
- **Communication**: Telegram Bot API, Discord Webhooks, SMTP

## Development & Deployment
- **Container Runtime**: Docker + Docker Compose
- **Orchestration**: Kubernetes (Production) / Docker Swarm (Development)
- **API Gateway**: Nginx + rate limiting
- **Monitoring**: Prometheus + Grafana + AlertManager
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)

## Common Commands
```bash
# Development setup
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Run the system
python -m agents.agent_coordinator

# Database migrations (if using Alembic)
alembic upgrade head

# Docker deployment
docker-compose up -d

# Run tests
pytest tests/

# Performance monitoring
docker-compose logs -f memeguard-api
```

## Performance Targets
- Detection latency: < 10 seconds
- Analysis completion: < 30 seconds parallel processing
- End-to-end processing: < 60 seconds
- System throughput: > 100 tokens/hour
- API response time: < 200ms (p95)