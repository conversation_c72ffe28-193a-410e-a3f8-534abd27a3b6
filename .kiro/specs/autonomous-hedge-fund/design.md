# Design Document

## Overview

The Autonomous Hedge Fund system is architected as a distributed, event-driven, multi-agent SaaS platform that functions as an intelligent cryptocurrency investment analysis engine. The system leverages CrewAI 2025 for multi-agent orchestration, with DeepSeek R1-0528 (free) and Llama-3.3-70B-Versatile as the exclusive LLM models, implementing PhD-level analysis through sophisticated prompt engineering and chain-of-thought reasoning.

The architecture follows a four-tier agent hierarchy: Detection → Analysis → Verification → Execution, with Byzantine fault tolerance consensus mechanisms and sub-60 second processing pipelines from token discovery to actionable trading alerts.

## Architecture

### High-Level System Architecture

```mermaid
graph TB
    subgraph "External Data Sources"
        A[Helius Solana API]
        B[Infura Ethereum/Base]
        C[DexScreener API]
        D[Birdeye API]
        E[Apify Twitter API]
        F[GoPlus Security API]
    end
    
    subgraph "API Gateway Layer"
        G[FastAPI Gateway]
        H[Rate Limiter]
        I[Authentication]
        J[Load Balancer]
    end
    
    subgraph "Multi-Agent Core System"
        K[Event Router & Queue Manager]
        L[CrewAI Orchestrator]
        
        subgraph "Agent Crews"
            M[Detection Crew - Agents 1-4]
            N[Analysis Crew - Agents 5-10]
            O[Verification Crew - Agents 11-15]
            P[Execution Crew - Agents 16-20]
        end
    end
    
    subgraph "Data Layer"
        Q[Redis Cache & Queues]
        R[PostgreSQL + TimescaleDB]
        S[ChromaDB Vector Store]
    end
    
    subgraph "External Services"
        T[OpenRouter API - DeepSeek R1]
        U[Groq API - Llama 3.3 70B]
        V[Telegram Bot API]
        W[Discord Webhooks]
        X[SMTP Email Service]
    end
    
    A --> G
    B --> G
    C --> G
    D --> G
    E --> G
    F --> G
    
    G --> K
    H --> G
    I --> G
    J --> G
    
    K --> L
    L --> M
    M --> N
    N --> O
    O --> P
    
    L --> Q
    L --> R
    L --> S
    
    L --> T
    L --> U
    
    P --> V
    P --> W
    P --> X
```

### Agent Distribution Strategy

**Model Assignment Philosophy:**
- **DeepSeek R1-0528 (Free)**: Complex reasoning, security analysis, consensus verification
- **Llama-3.3-70B-Versatile (Groq)**: Real-time detection, social sentiment, alert generation

## Components and Interfaces

### 1. Detection Crew (Agents 1-4)

#### Agent 1: Solana Token Detector
- **Model**: Llama-3.3-70B-Versatile (Groq)
- **Purpose**: Real-time Solana token discovery via Helius WebSocket
- **Tools**: Helius RPC, DexScreener API, Pump.fun monitoring
- **Output Schema**: `{token_address, liquidity_usd, creation_time, dex_info}`

#### Agent 2: Ethereum/Base Token Detector  
- **Model**: Llama-3.3-70B-Versatile (Groq)
- **Purpose**: EVM chain token discovery via Infura WebSocket
- **Tools**: Infura WebSocket, Uniswap V3 subgraph, 1inch API
- **Output Schema**: `{token_address, chain_id, liquidity_eth, pool_address}`

#### Agent 3: Social Signal Detector
- **Model**: Llama-3.3-70B-Versatile (Groq)
- **Purpose**: Social media buzz detection and sentiment scoring
- **Tools**: Apify Twitter Scraper, Reddit API, Telegram monitoring
- **Output Schema**: `{token_symbol, sentiment_score, mention_count, influencer_mentions}`

#### Agent 4: Whale Activity Detector
- **Model**: Llama-3.3-70B-Versatile (Groq)
- **Purpose**: Large transaction and wallet movement detection
- **Tools**: Birdeye Holders API, Nansen Smart Money feeds
- **Output Schema**: `{wallet_address, transaction_size, wallet_classification, risk_score}`

### 2. Analysis Crew (Agents 5-10)

#### Agent 5: Security Analyst
- **Model**: DeepSeek R1-0528 (Free)
- **Purpose**: Comprehensive smart contract security analysis
- **Tools**: GoPlus Security API, custom honeypot detection, bytecode analysis
- **Chain-of-Thought Prompt**: 
```
Analyze token security step-by-step:
1. Contract verification status and source code availability
2. Ownership renouncement and admin functions
3. Liquidity lock status and duration
4. Trading restrictions and honeypot indicators
5. Historical rug pull pattern matching
Provide quantitative risk score 0-100 with evidence.
```

#### Agent 6: Liquidity Analyst
- **Model**: DeepSeek R1-0528 (Free)
- **Purpose**: Deep liquidity analysis and sustainability assessment
- **Tools**: DEX APIs, historical price data, volume analysis
- **Chain-of-Thought Prompt**:
```
Evaluate liquidity health systematically:
1. Total liquidity depth across all DEXs
2. Liquidity concentration and distribution
3. Price impact analysis for various trade sizes
4. Historical liquidity trends and stability
5. Impermanent loss risk assessment
Calculate liquidity score 0-100 with supporting metrics.
```

#### Agent 7: Technical Analyst
- **Model**: DeepSeek R1-0528 (Free)
- **Purpose**: Technical indicator analysis and price prediction
- **Tools**: TradingView API, custom TA library, historical OHLCV data
- **Output Schema**: `{rsi, macd, bollinger_bands, support_resistance, trend_direction}`

#### Agent 8: Tokenomics Analyst
- **Model**: DeepSeek R1-0528 (Free)
- **Purpose**: Token distribution and economic model analysis
- **Tools**: Token holder analysis, vesting schedule detection
- **Output Schema**: `{total_supply, circulating_supply, holder_distribution, vesting_risk}`

#### Agent 9: Market Correlation Analyst
- **Model**: Llama-3.3-70B-Versatile (Groq)
- **Purpose**: Cross-asset correlation and market positioning
- **Tools**: CoinGecko API, correlation calculation libraries
- **Output Schema**: `{btc_correlation, eth_correlation, sector_correlation, beta_coefficient}`

#### Agent 10: Fundamental Analyst
- **Model**: DeepSeek R1-0528 (Free)
- **Purpose**: Project fundamentals and team analysis
- **Tools**: GitHub API, team verification, roadmap analysis
- **Output Schema**: `{team_score, development_activity, roadmap_quality, community_strength}`

### 3. Verification Crew (Agents 11-15)

#### Agent 11: Hallucination Guardian
- **Model**: DeepSeek R1-0528 (Free)
- **Purpose**: Detect and prevent AI hallucinations in analysis
- **Chain-of-Thought Prompt**:
```
Verify each claim against source data:
1. Cross-reference all numeric values with original API responses
2. Validate logical consistency between different analysis components
3. Check for unsupported speculation or assumptions
4. Ensure all conclusions are backed by quantitative evidence
5. Flag any claims that cannot be verified from source data
Return groundedness_score 0.0-1.0 with specific issues identified.
```

#### Agent 12: Consensus Coordinator
- **Model**: DeepSeek R1-0528 (Free)
- **Purpose**: Byzantine fault tolerance implementation and voting coordination
- **Algorithm**: Modified PBFT with 3f+1 fault tolerance
- **Output Schema**: `{consensus_reached, confidence_score, dissenting_opinions}`

#### Agent 13: Risk Validator
- **Model**: DeepSeek R1-0528 (Free)
- **Purpose**: Final risk assessment validation and scoring
- **Tools**: Historical rug pull database, pattern matching algorithms
- **Output Schema**: `{final_risk_score, risk_factors, confidence_interval}`

#### Agent 14: Performance Validator
- **Model**: Llama-3.3-70B-Versatile (Groq)
- **Purpose**: Validate system performance metrics and SLA compliance
- **Tools**: Prometheus metrics, latency tracking
- **Output Schema**: `{processing_time, sla_compliance, bottleneck_analysis}`

#### Agent 15: Compliance Checker
- **Model**: DeepSeek R1-0528 (Free)
- **Purpose**: Regulatory compliance and PII detection
- **Tools**: PII detection regex, compliance rule engine
- **Output Schema**: `{compliance_status, pii_detected, regulatory_flags}`

### 4. Execution Crew (Agents 16-20)

#### Agent 16: Strategy Generator
- **Model**: DeepSeek R1-0528 (Free)
- **Purpose**: Generate personalized trading strategies based on analysis
- **Chain-of-Thought Prompt**:
```
Generate trading strategy systematically:
1. Analyze risk-reward ratio based on all available data
2. Calculate optimal position sizing using Kelly Criterion
3. Determine entry, stop-loss, and take-profit levels
4. Assess market timing and execution strategy
5. Provide risk management recommendations
Output structured trading plan with quantitative parameters.
```

#### Agent 17: Alert Dispatcher
- **Model**: Llama-3.3-70B-Versatile (Groq)
- **Purpose**: Multi-channel alert generation and delivery
- **Tools**: Telegram Bot API, Discord webhooks, SMTP
- **Output Schema**: `{alert_sent, delivery_status, latency_metrics}`

#### Agent 18: Portfolio Manager
- **Model**: DeepSeek R1-0528 (Free)
- **Purpose**: Portfolio optimization and risk management
- **Tools**: Modern Portfolio Theory calculations, risk parity algorithms
- **Output Schema**: `{portfolio_allocation, risk_metrics, rebalancing_recommendations}`

#### Agent 19: Performance Monitor
- **Model**: Llama-3.3-70B-Versatile (Groq)
- **Purpose**: System health monitoring and performance tracking
- **Tools**: Prometheus, Grafana, custom metrics
- **Output Schema**: `{system_health, performance_metrics, alert_recommendations}`

#### Agent 20: Learning Coordinator
- **Model**: DeepSeek R1-0528 (Free)
- **Purpose**: Continuous learning and model improvement coordination
- **Tools**: Performance feedback loops, strategy backtesting
- **Output Schema**: `{learning_insights, model_updates, performance_improvements}`

## Data Models

### Core Token Model
```python
@dataclass
class TokenAnalysis:
    # Basic Information
    token_address: str
    chain_id: int
    symbol: str
    name: str
    creation_timestamp: datetime
    
    # Detection Data
    initial_liquidity_usd: float
    dex_info: Dict[str, Any]
    discovery_source: str
    
    # Analysis Results
    security_score: float  # 0-100
    liquidity_score: float  # 0-100
    technical_score: float  # 0-100
    social_sentiment: float  # -1 to 1
    fundamental_score: float  # 0-100
    
    # Risk Assessment
    overall_risk_score: float  # 0-100
    risk_factors: List[str]
    confidence_score: float  # 0-1
    
    # Trading Strategy
    entry_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    position_size: Optional[float]
    
    # Metadata
    processing_time: float
    agent_consensus: Dict[str, float]
    verification_status: str
```

### Agent Communication Model
```python
@dataclass
class AgentMessage:
    agent_id: str
    message_type: str  # 'detection', 'analysis', 'verification', 'execution'
    payload: Dict[str, Any]
    timestamp: datetime
    correlation_id: str
    priority: int  # 1-5, 1 being highest
```

### Performance Metrics Model
```python
@dataclass
class SystemMetrics:
    detection_latency_p95: float
    analysis_completion_time: float
    consensus_success_rate: float
    alert_delivery_latency: float
    api_cost_per_token: float
    hallucination_rate: float
    system_uptime: float
```

## Error Handling

### Circuit Breaker Pattern Implementation
```python
class CircuitBreaker:
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
```

### Retry Strategy
- **Exponential Backoff**: Base delay 1s, max delay 60s, max retries 5
- **Jitter**: Random delay ±20% to prevent thundering herd
- **Circuit Breaker**: Open circuit after 5 consecutive failures

### Graceful Degradation
1. **API Failures**: Use cached data when available
2. **Agent Failures**: Continue with remaining agents, flag incomplete analysis
3. **Database Failures**: Use Redis as temporary storage
4. **LLM Failures**: Implement model fallback chain

## Testing Strategy

### Unit Testing
- **Agent Behavior**: Mock external APIs, test prompt responses
- **Data Models**: Validate schema compliance and data integrity
- **Utility Functions**: Test mathematical calculations and transformations

### Integration Testing
- **API Integration**: Test all external API endpoints with real data
- **Agent Communication**: Verify message passing and state management
- **Database Operations**: Test CRUD operations and data consistency

### Performance Testing
- **Load Testing**: Simulate 100+ concurrent token analyses
- **Stress Testing**: Test system behavior under resource constraints
- **Latency Testing**: Verify sub-60 second processing requirements

### Hallucination Testing
- **Adversarial Prompts**: 300+ test cases designed to trigger hallucinations
- **Groundedness Validation**: Automated fact-checking against source data
- **Consensus Testing**: Verify Byzantine fault tolerance under conflicting inputs

### End-to-End Testing
- **Token Discovery**: Monitor live blockchain data for new tokens
- **Full Pipeline**: Test complete flow from detection to alert delivery
- **Alert Validation**: Verify alert accuracy and delivery confirmation

## Security Considerations

### API Key Management
- **Encryption**: AES-256 encryption for all stored credentials
- **Rotation**: Automated monthly key rotation
- **Segregation**: Separate keys for different environments

### Data Privacy
- **PII Detection**: Automated scanning and redaction
- **Audit Logging**: Comprehensive activity tracking
- **Access Control**: Role-based permissions and authentication

### Network Security
- **TLS 1.3**: All external communications encrypted
- **Rate Limiting**: Prevent abuse and DoS attacks
- **Input Validation**: Sanitize all external data inputs

## Monitoring and Observability

### Metrics Collection
- **Business Metrics**: Token detection rate, analysis accuracy, profit/loss tracking
- **Technical Metrics**: API latency, error rates, resource utilization
- **Cost Metrics**: API usage costs, infrastructure expenses

### Alerting Strategy
- **Critical Alerts**: System failures, security breaches, SLA violations
- **Warning Alerts**: Performance degradation, approaching rate limits
- **Info Alerts**: Daily summaries, performance reports

### Dashboards
- **Operations Dashboard**: System health, performance metrics, error rates
- **Business Dashboard**: Token analysis results, trading performance, ROI
- **Cost Dashboard**: API usage, infrastructure costs, budget tracking