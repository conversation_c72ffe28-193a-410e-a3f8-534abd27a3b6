#!/usr/bin/env python3
"""
MemeGuard Pro v4.0 - Main Entry Point
Autonomous Hedge Fund Intelligence Platform

This is the main entry point for the MemeGuard Pro system.
It initializes the multi-agent coordinator and starts the detection pipeline.
"""

import asyncio
import signal
import sys
import os
import argparse
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables from .env file
load_dotenv()

# Import core infrastructure
from core import setup_logging, get_settings, validate_required_settings
from core.exceptions import MemeGuardError, ConfigurationError
from agents.agent_coordinator import AgentSystemManager


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="MemeGuard Pro v4.0 - Autonomous Hedge Fund Intelligence Platform",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python main.py                          # Start with default settings
    python main.py --env .env.production    # Use production environment
    python main.py --debug                  # Enable debug mode
    python main.py --health-check           # Run health check and exit
    
For more information, visit: https://github.com/memeguard-pro
        """
    )
    
    parser.add_argument(
        '--env',
        type=str,
        default='.env',
        help='Environment file path (default: .env)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode'
    )
    
    parser.add_argument(
        '--health-check',
        action='store_true',
        help='Run health check and exit'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='MemeGuard Pro v4.0.0'
    )
    
    return parser.parse_args()


def initialize_system(env_file: str = '.env', debug: bool = False):
    """Initialize system configuration and logging"""
    try:
        # Set environment file if provided
        if env_file != '.env':
            os.environ['ENV_FILE'] = env_file
        
        # Override debug setting if provided
        if debug:
            os.environ['DEBUG'] = 'true'
        
        # Load and validate settings
        settings = get_settings()
        validate_required_settings()
        
        # Setup logging infrastructure
        log_dir = "logs" if not settings.is_development() else None
        setup_logging(
            log_level=settings.log_level,
            environment=settings.environment,
            sentry_dsn=settings.sentry_dsn,
            debug=settings.debug,
            log_dir=log_dir,
        )
        
        return settings
        
    except ConfigurationError as e:
        print(f"❌ Configuration Error: {e}")
        print("Please check your .env file and ensure all required settings are provided.")
        print("See .env.example for a template with all required variables.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ System Initialization Error: {e}")
        sys.exit(1)


async def health_check():
    """Perform system health check"""
    print("🔍 Performing system health check...")
    
    try:
        # Initialize system
        settings = initialize_system()
        
        # Get logger
        from core import get_logger
        logger = get_logger(__name__)
        
        # Test configuration
        logger.info("Testing configuration...")
        
        # Test API keys
        required_keys = ['openrouter', 'helius', 'infura']
        for key in required_keys:
            api_key = settings.get_api_key(key)
            if api_key:
                logger.info(f"✅ {key.upper()} API key configured")
            else:
                logger.error(f"❌ {key.upper()} API key missing")
                return False
        
        # Test database configuration
        logger.info(f"Database URL: {settings.get_database_url()}")
        
        # Test Redis configuration
        logger.info(f"Redis URL: {settings.get_redis_url()}")
        
        print("✅ System health check passed")
        return True
        
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False


async def main():
    """Main entry point for MemeGuard Pro"""
    args = parse_arguments()
    
    print("🚀 Starting MemeGuard Pro v4.0...")
    print("🤖 Initializing 20-agent autonomous hedge fund system...")
    
    # Handle health check
    if args.health_check:
        success = await health_check()
        sys.exit(0 if success else 1)
    
    # Initialize system
    settings = initialize_system(args.env, args.debug)
    
    # Get logger after initialization
    from core import get_logger
    logger = get_logger(__name__)
    
    logger.info(
        "MemeGuard Pro starting up",
        extra={
            'version': '4.0.0',
            'environment': settings.environment,
            'debug': settings.debug,
        }
    )
    
    # Initialize the crew coordinator
    try:
        coordinator = AgentSystemManager()
        logger.info("Crew coordinator initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize crew coordinator: {e}")
        raise
    
    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.warning(f"Received signal {signum}, initiating graceful shutdown")
        print(f"\n⚠️  Received signal {signum}, shutting down gracefully...")
        asyncio.create_task(coordinator.shutdown())
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start the coordinator
        logger.info("Starting crew coordinator")
        
        # Initialize the system first
        await coordinator.initialize()
        logger.info("Crew coordinator initialization complete")
        
        # Then start the system
        await coordinator.start()
        
        # Keep the main process running
        logger.info("MemeGuard Pro is now running")
        print("✅ MemeGuard Pro is now running. Press Ctrl+C to stop.")
        
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, shutting down")
        print("\n⚠️  Keyboard interrupt received, shutting down...")
    except MemeGuardError as e:
        logger.error(f"MemeGuard error: {e}", extra=e.to_dict())
        print(f"❌ MemeGuard Error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
    finally:
        logger.info("Shutting down crew coordinator")
        await coordinator.shutdown()
        logger.info("MemeGuard Pro shutdown complete")
        print("✅ MemeGuard Pro shutdown complete")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)