# Project Structure

## Root Directory
```
├── .env                    # Environment variables (API keys, database URLs)
├── .kiro/                  # Kiro IDE configuration and steering rules
├── agents/                 # Core multi-agent system implementation
├── blueprint_prd.md        # Product requirements document
└── system_architecture.md  # Comprehensive technical architecture
```

## Agent System Organization
The `agents/` directory contains the multi-agent framework with specialized agent types:

- **Detection Agents (1-4)**: Real-time token discovery across blockchains
  - `SolanaDetectorDEXScreener`, `SolanaDetectorBirdeye`
  - `EthereumDetector`, `BaseChainDetector`

- **Analysis Agents (5-10)**: Parallel analysis of detected tokens
  - `SecurityAnalyst`, `HoneypotDetector`, `LiquidityAnalyst`
  - `DistributionAnalyst`, `SocialSentimentAnalyst`, `WhaleWatcherAnalyst`

- **Verification Agents (11-15)**: Consensus-based validation
  - `ConsensusVerifier` (Byzantine fault tolerance implementation)

- **Execution Agents (16-20)**: Strategy generation and alert dispatch
  - `StrategyGenerator`, `AlertDispatcher`, `PersonalizationAgent`
  - `PerformanceLogger`, `HealthMonitor`

## Key Architectural Patterns

### Agent Communication
- Event-driven architecture with Redis queues
- Parallel processing for detection and analysis phases
- Sequential consensus verification before execution
- Cross-agent logging and health monitoring

### Data Flow
1. **Detection**: WebSocket streams → Detection agents → Analysis queue
2. **Analysis**: Parallel processing by 6 specialized agents → Verification queue
3. **Verification**: Byzantine consensus voting → Execution queue
4. **Execution**: Strategy generation → Multi-channel alert dispatch

### Code Organization Principles
- Each agent type has distinct responsibilities and interfaces
- Chain-of-thought reasoning templates for consistent analysis quality
- Centralized coordination through `MemeGuardCrewCoordinator`
- Modular design allowing independent agent scaling and updates

## Configuration Management
- Environment variables in `.env` for API keys and database connections
- Agent-specific configurations embedded in class definitions
- Performance targets and thresholds defined in architecture documentation
- Steering rules in `.kiro/steering/` for AI assistant guidance